#!/bin/bash

# InstrumentCapture iOS App Setup Script
# This script helps prepare the iOS project for Xcode

echo "🚀 Setting up InstrumentCapture iOS App..."

# Check if we're in the right directory
if [ ! -f "InstrumentCapture.xcodeproj/project.pbxproj" ]; then
    echo "❌ Error: Please run this script from the ios-app directory"
    exit 1
fi

# Make sure Xcode is installed
if ! command -v xcodebuild &> /dev/null; then
    echo "❌ Error: Xcode is not installed or not in PATH"
    exit 1
fi

echo "✅ Xcode found"

# Check iOS deployment target
echo "📱 Checking iOS deployment target..."
DEPLOYMENT_TARGET=$(xcodebuild -project InstrumentCapture.xcodeproj -showBuildSettings | grep IPHONEOS_DEPLOYMENT_TARGET | head -1 | awk '{print $3}')
echo "   Current deployment target: $DEPLOYMENT_TARGET"

# Validate project structure
echo "📁 Validating project structure..."

required_files=(
    "InstrumentCapture/AppDelegate.swift"
    "InstrumentCapture/SceneDelegate.swift"
    "InstrumentCapture/ViewController.swift"
    "InstrumentCapture/CameraManager.swift"
    "InstrumentCapture/NetworkManager.swift"
    "InstrumentCapture/ImageBatchManager.swift"
    "InstrumentCapture/Info.plist"
    "InstrumentCapture/Base.lproj/Main.storyboard"
    "InstrumentCapture/Base.lproj/LaunchScreen.storyboard"
    "InstrumentCapture/Assets.xcassets/Contents.json"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All required files present"
else
    echo "❌ Missing files:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

# Check if backend is running
echo "🔍 Checking backend connection..."
if curl -s http://localhost:8080/api/status > /dev/null 2>&1; then
    echo "✅ Backend is running on port 8080"
else
    echo "⚠️  Backend not detected on port 8080"
    echo "   Make sure to start the backend server before testing the app"
fi

# Display next steps
echo ""
echo "🎉 Setup complete! Next steps:"
echo ""
echo "1. Open Xcode:"
echo "   open InstrumentCapture.xcodeproj"
echo ""
echo "2. Configure signing:"
echo "   - Select the project in navigator"
echo "   - Go to 'Signing & Capabilities'"
echo "   - Set your Development Team"
echo "   - Update Bundle Identifier if needed"
echo ""
echo "3. Connect your iPhone and run the app"
echo ""
echo "4. Start the backend server if not already running:"
echo "   cd ../backend && npm run dev"
echo ""
echo "5. Open the frontend to test the complete system:"
echo "   cd ../frontend-assistant-ui && npm run dev"
echo ""

# Optional: Open Xcode automatically
read -p "Would you like to open Xcode now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Opening Xcode..."
    open InstrumentCapture.xcodeproj
fi

echo "📖 For detailed instructions, see README.md"
