import AVFoundation
import UIKit
import Vision

protocol CameraManagerDelegate: AnyObject {
    func cameraManager(_ manager: CameraMana<PERSON>, didCaptureImage image: UIImage)
    func cameraManager(_ manager: CameraManager, didFailWithError error: Error)
    func cameraManagerDidStartSession(_ manager: CameraManager)
    func cameraManagerDidStopSession(_ manager: CameraManager)
}

class CameraManager: NSObject {
    
    // MARK: - Properties
    weak var delegate: CameraManagerDelegate?
    
    private let captureSession = AVCaptureSession()
    private var photoOutput = AVCapturePhotoOutput()
    private var videoPreviewLayer: AVCaptureVideoPreviewLayer?
    private var currentDevice: AVCaptureDevice?
    
    // Camera settings
    private var currentZoomFactor: CGFloat = 0.5 // Default to 0.5x wide-angle
    private var isSessionRunning = false
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupCaptureSession()
    }
    
    // MARK: - Public Methods
    func startSession() {
        guard !isSessionRunning else { return }
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession.startRunning()
            DispatchQueue.main.async {
                self?.isSessionRunning = true
                self?.delegate?.cameraManagerDidStartSession(self!)
            }
        }
    }
    
    func stopSession() {
        guard isSessionRunning else { return }
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.captureSession.stopRunning()
            DispatchQueue.main.async {
                self?.isSessionRunning = false
                self?.delegate?.cameraManagerDidStopSession(self!)
            }
        }
    }
    
    func capturePhoto() {
        guard isSessionRunning else {
            delegate?.cameraManager(self, didFailWithError: CameraError.sessionNotRunning)
            return
        }
        
        let settings = AVCapturePhotoSettings()
        
        // Use highest quality format available
        if photoOutput.availablePhotoCodecTypes.contains(.hevc) {
            settings = AVCapturePhotoSettings(format: [AVVideoCodecKey: AVVideoCodecType.hevc])
        }
        
        // Enable high resolution capture
        settings.isHighResolutionPhotoEnabled = true
        
        // Disable flash for screen capture
        settings.flashMode = .off
        
        // Auto settings for optimal screen capture
        settings.isAutoStillImageStabilizationEnabled = photoOutput.isStillImageStabilizationSupported
        
        photoOutput.capturePhoto(with: settings, delegate: self)
    }
    
    func setZoomFactor(_ factor: CGFloat) {
        guard let device = currentDevice else { return }
        
        do {
            try device.lockForConfiguration()
            let clampedFactor = max(device.minAvailableVideoZoomFactor, 
                                  min(factor, device.maxAvailableVideoZoomFactor))
            device.videoZoomFactor = clampedFactor
            currentZoomFactor = clampedFactor
            device.unlockForConfiguration()
        } catch {
            delegate?.cameraManager(self, didFailWithError: error)
        }
    }
    
    func getPreviewLayer() -> AVCaptureVideoPreviewLayer? {
        return videoPreviewLayer
    }
    
    // MARK: - Private Methods
    private func setupCaptureSession() {
        // Configure session for high quality
        captureSession.sessionPreset = .photo
        
        // Setup camera input
        guard let camera = getBestCamera() else {
            delegate?.cameraManager(self, didFailWithError: CameraError.noCameraAvailable)
            return
        }
        
        currentDevice = camera
        
        do {
            let input = try AVCaptureDeviceInput(device: camera)
            if captureSession.canAddInput(input) {
                captureSession.addInput(input)
            }
        } catch {
            delegate?.cameraManager(self, didFailWithError: error)
            return
        }
        
        // Setup photo output
        if captureSession.canAddOutput(photoOutput) {
            captureSession.addOutput(photoOutput)
            
            // Configure for maximum quality
            photoOutput.isHighResolutionCaptureEnabled = true
            photoOutput.maxPhotoQualityPrioritization = .quality
        }
        
        // Setup preview layer
        videoPreviewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        videoPreviewLayer?.videoGravity = .resizeAspectFill
        
        // Configure camera for optimal screen capture
        configureCameraForScreenCapture()
    }
    
    private func getBestCamera() -> AVCaptureDevice? {
        // Prefer triple camera system on iPhone 14+ for best quality
        if let tripleCamera = AVCaptureDevice.default(.builtInTripleCamera, for: .video, position: .back) {
            return tripleCamera
        }
        
        // Fallback to dual wide camera
        if let dualWideCamera = AVCaptureDevice.default(.builtInDualWideCamera, for: .video, position: .back) {
            return dualWideCamera
        }
        
        // Fallback to wide angle camera
        return AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back)
    }
    
    private func configureCameraForScreenCapture() {
        guard let device = currentDevice else { return }
        
        do {
            try device.lockForConfiguration()
            
            // Set focus mode for screen capture (infinity focus works well for screens)
            if device.isFocusModeSupported(.continuousAutoFocus) {
                device.focusMode = .continuousAutoFocus
            }
            
            // Set exposure mode for consistent lighting
            if device.isExposureModeSupported(.continuousAutoExposure) {
                device.exposureMode = .continuousAutoExposure
            }
            
            // Set white balance for screen content
            if device.isWhiteBalanceModeSupported(.continuousAutoWhiteBalance) {
                device.whiteBalanceMode = .continuousAutoWhiteBalance
            }
            
            // Set initial zoom to 0.5x for wide coverage
            setZoomFactor(0.5)
            
            device.unlockForConfiguration()
        } catch {
            delegate?.cameraManager(self, didFailWithError: error)
        }
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension CameraManager: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput, didFinishProcessingPhoto photo: AVCapturePhoto, error: Error?) {
        if let error = error {
            delegate?.cameraManager(self, didFailWithError: error)
            return
        }
        
        guard let imageData = photo.fileDataRepresentation(),
              let image = UIImage(data: imageData) else {
            delegate?.cameraManager(self, didFailWithError: CameraError.imageProcessingFailed)
            return
        }
        
        // Apply image enhancements for screen capture
        let enhancedImage = enhanceImageForScreenCapture(image)
        delegate?.cameraManager(self, didCaptureImage: enhancedImage)
    }
    
    private func enhanceImageForScreenCapture(_ image: UIImage) -> UIImage {
        // Apply contrast and sharpening filters for better text readability
        guard let ciImage = CIImage(image: image) else { return image }
        
        let context = CIContext()
        
        // Apply contrast adjustment
        let contrastFilter = CIFilter(name: "CIColorControls")!
        contrastFilter.setValue(ciImage, forKey: kCIInputImageKey)
        contrastFilter.setValue(1.2, forKey: kCIInputContrastKey) // Increase contrast
        contrastFilter.setValue(1.1, forKey: kCIInputSaturationKey) // Slight saturation boost
        
        guard let contrastOutput = contrastFilter.outputImage else { return image }
        
        // Apply sharpening
        let sharpenFilter = CIFilter(name: "CIUnsharpMask")!
        sharpenFilter.setValue(contrastOutput, forKey: kCIInputImageKey)
        sharpenFilter.setValue(0.5, forKey: kCIInputIntensityKey)
        sharpenFilter.setValue(2.5, forKey: kCIInputRadiusKey)
        
        guard let finalOutput = sharpenFilter.outputImage,
              let cgImage = context.createCGImage(finalOutput, from: finalOutput.extent) else {
            return image
        }
        
        return UIImage(cgImage: cgImage)
    }
}

// MARK: - Camera Errors
enum CameraError: LocalizedError {
    case noCameraAvailable
    case sessionNotRunning
    case imageProcessingFailed
    
    var errorDescription: String? {
        switch self {
        case .noCameraAvailable:
            return "No camera available on this device"
        case .sessionNotRunning:
            return "Camera session is not running"
        case .imageProcessingFailed:
            return "Failed to process captured image"
        }
    }
}
