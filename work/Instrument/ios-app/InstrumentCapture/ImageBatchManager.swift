import UIKit
import Foundation

protocol ImageBatchManagerDelegate: AnyObject {
    func imageBatchManager(_ manager: Image<PERSON>atchManager, didUpdateBatchCount count: Int)
    func imageBatchManager(_ manager: ImageBatchManager, didFailWithError error: Error)
}

struct CapturedImage {
    let id: UUID
    let image: UIImage
    let timestamp: Date
    let metadata: [String: Any]
    
    init(image: UIImage, metadata: [String: Any] = [:]) {
        self.id = UUID()
        self.image = image
        self.timestamp = Date()
        self.metadata = metadata
    }
}

class ImageBatchManager {
    
    // MARK: - Properties
    weak var delegate: ImageBatchManagerDelegate?
    
    private var currentBatch: [CapturedImage] = []
    private let maxBatchSize: Int = 10
    private let maxImageSize: Int = 5 * 1024 * 1024 // 5MB per image
    private let compressionQuality: CGFloat = 0.8
    
    private let documentsDirectory: URL
    private let batchDirectory: URL
    
    // MARK: - Initialization
    init() {
        documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        batchDirectory = documentsDirectory.appendingPathComponent("ImageBatches")
        
        createBatchDirectoryIfNeeded()
    }
    
    // MARK: - Public Methods
    func addImage(_ image: UIImage, metadata: [String: Any] = [:]) {
        let capturedImage = CapturedImage(image: image, metadata: metadata)
        currentBatch.append(capturedImage)
        
        // Limit batch size
        if currentBatch.count > maxBatchSize {
            currentBatch.removeFirst()
        }
        
        delegate?.imageBatchManager(self, didUpdateBatchCount: currentBatch.count)
        
        print("📸 Added image to batch. Current count: \(currentBatch.count)")
    }
    
    func getCurrentBatch() -> [CapturedImage] {
        return currentBatch
    }
    
    func getCurrentBatchCount() -> Int {
        return currentBatch.count
    }
    
    func clearCurrentBatch() {
        currentBatch.removeAll()
        delegate?.imageBatchManager(self, didUpdateBatchCount: 0)
        print("🗑️ Cleared current batch")
    }
    
    func getCompressedImageData() -> [Data] {
        return currentBatch.compactMap { capturedImage in
            return compressImage(capturedImage.image)
        }
    }
    
    func getBase64EncodedImages() -> [String] {
        return getCompressedImageData().map { $0.base64EncodedString() }
    }
    
    func saveBatchToDisk() -> URL? {
        guard !currentBatch.isEmpty else { return nil }
        
        let batchId = UUID().uuidString
        let batchURL = batchDirectory.appendingPathComponent(batchId)
        
        do {
            try FileManager.default.createDirectory(at: batchURL, withIntermediateDirectories: true)
            
            var savedImages: [String] = []
            
            for (index, capturedImage) in currentBatch.enumerated() {
                let filename = "image_\(index)_\(capturedImage.id.uuidString).jpg"
                let imageURL = batchURL.appendingPathComponent(filename)
                
                if let imageData = compressImage(capturedImage.image) {
                    try imageData.write(to: imageURL)
                    savedImages.append(filename)
                }
            }
            
            // Save batch metadata
            let metadata: [String: Any] = [
                "batchId": batchId,
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "imageCount": savedImages.count,
                "images": savedImages
            ]
            
            let metadataURL = batchURL.appendingPathComponent("metadata.json")
            let metadataData = try JSONSerialization.data(withJSONObject: metadata)
            try metadataData.write(to: metadataURL)
            
            print("💾 Saved batch to disk: \(batchURL.path)")
            return batchURL
            
        } catch {
            delegate?.imageBatchManager(self, didFailWithError: error)
            return nil
        }
    }
    
    func loadBatchFromDisk(batchURL: URL) -> [CapturedImage]? {
        let metadataURL = batchURL.appendingPathComponent("metadata.json")
        
        do {
            let metadataData = try Data(contentsOf: metadataURL)
            let metadata = try JSONSerialization.jsonObject(with: metadataData) as? [String: Any]
            
            guard let imageFilenames = metadata?["images"] as? [String] else {
                return nil
            }
            
            var loadedImages: [CapturedImage] = []
            
            for filename in imageFilenames {
                let imageURL = batchURL.appendingPathComponent(filename)
                if let imageData = try? Data(contentsOf: imageURL),
                   let image = UIImage(data: imageData) {
                    let capturedImage = CapturedImage(image: image)
                    loadedImages.append(capturedImage)
                }
            }
            
            print("📂 Loaded batch from disk: \(loadedImages.count) images")
            return loadedImages
            
        } catch {
            delegate?.imageBatchManager(self, didFailWithError: error)
            return nil
        }
    }
    
    func getSavedBatches() -> [URL] {
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: batchDirectory, 
                                                                      includingPropertiesForKeys: [.creationDateKey], 
                                                                      options: [.skipsHiddenFiles])
            return contents.filter { $0.hasDirectoryPath }
        } catch {
            return []
        }
    }
    
    func deleteBatch(at url: URL) {
        do {
            try FileManager.default.removeItem(at: url)
            print("🗑️ Deleted batch: \(url.lastPathComponent)")
        } catch {
            delegate?.imageBatchManager(self, didFailWithError: error)
        }
    }
    
    func cleanupOldBatches(olderThan days: Int = 7) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let savedBatches = getSavedBatches()
        
        for batchURL in savedBatches {
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: batchURL.path)
                if let creationDate = attributes[.creationDate] as? Date,
                   creationDate < cutoffDate {
                    deleteBatch(at: batchURL)
                }
            } catch {
                print("⚠️ Failed to check batch date: \(error)")
            }
        }
    }
    
    // MARK: - Private Methods
    private func createBatchDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: batchDirectory.path) {
            do {
                try FileManager.default.createDirectory(at: batchDirectory, withIntermediateDirectories: true)
            } catch {
                delegate?.imageBatchManager(self, didFailWithError: error)
            }
        }
    }
    
    private func compressImage(_ image: UIImage) -> Data? {
        // Start with the specified quality
        var quality = compressionQuality
        var imageData = image.jpegData(compressionQuality: quality)
        
        // Reduce quality until we're under the size limit
        while let data = imageData, data.count > maxImageSize && quality > 0.1 {
            quality -= 0.1
            imageData = image.jpegData(compressionQuality: quality)
        }
        
        // If still too large, resize the image
        if let data = imageData, data.count > maxImageSize {
            let scaleFactor = sqrt(Double(maxImageSize) / Double(data.count))
            let newSize = CGSize(width: image.size.width * scaleFactor, 
                               height: image.size.height * scaleFactor)
            
            if let resizedImage = resizeImage(image, to: newSize) {
                imageData = resizedImage.jpegData(compressionQuality: compressionQuality)
            }
        }
        
        return imageData
    }
    
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage
    }
}

// MARK: - Storage Management
extension ImageBatchManager {
    func getStorageInfo() -> [String: Any] {
        let batchCount = getSavedBatches().count
        let currentBatchSize = currentBatch.count
        
        // Calculate approximate storage usage
        var totalSize: Int64 = 0
        let savedBatches = getSavedBatches()
        
        for batchURL in savedBatches {
            if let attributes = try? FileManager.default.attributesOfItem(atPath: batchURL.path),
               let size = attributes[.size] as? Int64 {
                totalSize += size
            }
        }
        
        return [
            "savedBatches": batchCount,
            "currentBatchSize": currentBatchSize,
            "totalStorageBytes": totalSize,
            "totalStorageMB": Double(totalSize) / (1024 * 1024)
        ]
    }
    
    func isStorageAvailable() -> Bool {
        do {
            let attributes = try FileManager.default.attributesOfFileSystem(forPath: documentsDirectory.path)
            if let freeSpace = attributes[.systemFreeSize] as? Int64 {
                return freeSpace > 100 * 1024 * 1024 // Require at least 100MB free
            }
        } catch {
            print("⚠️ Failed to check storage: \(error)")
        }
        return false
    }
}
