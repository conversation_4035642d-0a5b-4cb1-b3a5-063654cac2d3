<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeArea="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21678"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="ViewController" customModule="InstrumentCapture" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hQf-2h-8Ey">
                                <rect key="frame" x="0.0" y="59" width="393" height="600"/>
                                <color key="backgroundColor" systemColor="blackColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="600" id="Xzh-Nh-fhd"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9gm-fd-0l3">
                                <rect key="frame" x="171.66666666666666" y="719" width="50" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="50" id="8Ub-Nh-fhd"/>
                                    <constraint firstAttribute="height" constant="50" id="Xzh-Nh-fhd"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="plain" title="📸"/>
                                <connections>
                                    <action selector="captureButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Xzh-Nh-fhd"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Camera ready" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xzh-Nh-fhd">
                                <rect key="frame" x="20" y="679" width="353" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" systemColor="labelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Batch: 0 images" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Ub-Nh-fhd">
                                <rect key="frame" x="20" y="789" width="353" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" systemColor="labelColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9gm-fd-0l3">
                                <rect key="frame" x="20" y="79" width="20" height="20"/>
                                <color key="backgroundColor" systemColor="systemRedColor"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="20" id="8Ub-Nh-fhd"/>
                                    <constraint firstAttribute="height" constant="20" id="Xzh-Nh-fhd"/>
                                </constraints>
                            </view>
                            <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.5" minValue="0.5" maxValue="3" translatesAutoresizingMaskIntoConstraints="NO" id="hQf-2h-8Ey">
                                <rect key="frame" x="18" y="109" width="357" height="31"/>
                                <connections>
                                    <action selector="zoomSliderChanged:" destination="BYZ-38-t0r" eventType="valueChanged" id="Xzh-Nh-fhd"/>
                                </connections>
                            </slider>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8Ub-Nh-fhd">
                                <rect key="frame" x="50" y="820" width="100" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="100" id="8Ub-Nh-fhd"/>
                                    <constraint firstAttribute="height" constant="35" id="Xzh-Nh-fhd"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="filled" title="Send Batch"/>
                                <connections>
                                    <action selector="sendButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Xzh-Nh-fhd"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9gm-fd-0l3">
                                <rect key="frame" x="243" y="820" width="100" height="35"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="100" id="8Ub-Nh-fhd"/>
                                    <constraint firstAttribute="height" constant="35" id="Xzh-Nh-fhd"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <buttonConfiguration key="configuration" style="filled" title="Clear"/>
                                <connections>
                                    <action selector="clearBatchButtonTapped:" destination="BYZ-38-t0r" eventType="touchUpInside" id="Xzh-Nh-fhd"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="hQf-2h-8Ey" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="2Ub-Nh-fhd"/>
                            <constraint firstItem="hQf-2h-8Ey" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="3Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="hQf-2h-8Ey" secondAttribute="trailing" id="4Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="5Ub-Nh-fhd"/>
                            <constraint firstItem="Xzh-Nh-fhd" firstAttribute="top" secondItem="hQf-2h-8Ey" secondAttribute="bottom" constant="20" id="6Ub-Nh-fhd"/>
                            <constraint firstItem="Xzh-Nh-fhd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="7Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Xzh-Nh-fhd" secondAttribute="trailing" constant="20" id="8Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="top" secondItem="Xzh-Nh-fhd" secondAttribute="bottom" constant="19" id="9Ub-Nh-fhd"/>
                            <constraint firstItem="8Ub-Nh-fhd" firstAttribute="top" secondItem="9gm-fd-0l3" secondAttribute="bottom" constant="20" id="10Ub-Nh-fhd"/>
                            <constraint firstItem="8Ub-Nh-fhd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="11Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="8Ub-Nh-fhd" secondAttribute="trailing" constant="20" id="12Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="13Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="14Ub-Nh-fhd"/>
                            <constraint firstItem="hQf-2h-8Ey" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="15Ub-Nh-fhd"/>
                            <constraint firstItem="hQf-2h-8Ey" firstAttribute="top" secondItem="9gm-fd-0l3" secondAttribute="bottom" constant="10" id="16Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="hQf-2h-8Ey" secondAttribute="trailing" constant="20" id="17Ub-Nh-fhd"/>
                            <constraint firstItem="8Ub-Nh-fhd" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="50" id="18Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="8Ub-Nh-fhd" secondAttribute="bottom" constant="17" id="19Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="leading" secondItem="8Ub-Nh-fhd" secondAttribute="trailing" constant="93" id="20Ub-Nh-fhd"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="9gm-fd-0l3" secondAttribute="trailing" constant="50" id="21Ub-Nh-fhd"/>
                            <constraint firstItem="9gm-fd-0l3" firstAttribute="top" secondItem="8Ub-Nh-fhd" secondAttribute="top" id="22Ub-Nh-fhd"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                    <connections>
                        <outlet property="batchCountLabel" destination="8Ub-Nh-fhd" id="23Ub-Nh-fhd"/>
                        <outlet property="captureButton" destination="9gm-fd-0l3" id="24Ub-Nh-fhd"/>
                        <outlet property="clearBatchButton" destination="9gm-fd-0l3" id="25Ub-Nh-fhd"/>
                        <outlet property="connectionStatusView" destination="9gm-fd-0l3" id="26Ub-Nh-fhd"/>
                        <outlet property="previewView" destination="hQf-2h-8Ey" id="27Ub-Nh-fhd"/>
                        <outlet property="sendButton" destination="8Ub-Nh-fhd" id="28Ub-Nh-fhd"/>
                        <outlet property="statusLabel" destination="Xzh-Nh-fhd" id="29Ub-Nh-fhd"/>
                        <outlet property="zoomSlider" destination="hQf-2h-8Ey" id="30Ub-Nh-fhd"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20" y="-2"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="blackColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
