"use client";

import { useEffect, useMemo, useState } from "react";
import { AssistantRuntimeProvider, useLocalRuntime } from "@assistant-ui/react";
import { Thread } from "@/components/assistant-ui/thread";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { Separator } from "@/components/ui/separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { InstrumentManager, type DeviceInfo } from "@/lib/instrument-runtime";
import { ImagePreview } from "@/components/assistant-ui/image-preview";

export const Assistant = () => {
  // Config - using different ports to avoid conflicts with Next.js dev server
  const BACKEND_URL = "http://localhost:8080";
  const WS_URL = "ws://localhost:8081";

  // Instrument state
  const [connectionStatus, setConnectionStatus] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [images, setImages] = useState<string[]>([]);
  const [instrumentManager, setInstrumentManager] = useState<InstrumentManager | null>(null);

  // Initialize InstrumentManager once
  useEffect(() => {
    const manager = new InstrumentManager({
      backendUrl: BACKEND_URL,
      wsUrl: WS_URL,
      onDeviceStatusChange: (connected, info) => {
        setConnectionStatus(connected);
        setDeviceInfo(info);
      },
      onImagesReceived: (imgs) => setImages(imgs),
    });

    setInstrumentManager(manager);
    return () => manager.destroy();
  }, []);

  // Fallback adapter while initializing
  const defaultAdapter = useMemo(
    () => ({
      async *run() {
        yield { content: [{ type: "text", text: "Initializing..." }] } as any;
      },
    }),
    []
  );

  // Local runtime backed by our Instrument adapter
  const runtime = useLocalRuntime(
    instrumentManager ? instrumentManager.createAdapter() : (defaultAdapter as any)
  );

  // Handlers
  const handleCapture = () => instrumentManager?.captureImages(1);
  const handleSend = () => instrumentManager?.sendToDevice();

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <SidebarProvider>
        <div className="flex h-dvh w-full pr-0.5">
          <AppSidebar />
          <SidebarInset>
            <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
              <SidebarTrigger />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="https://www.assistant-ui.com/docs/getting-started" target="_blank" rel="noopener noreferrer">
                      Build Your Own ChatGPT UX
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Starter Template</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              {/* Device status chip */}
              <div className="ml-auto rounded-full px-3 py-1 text-sm border flex items-center gap-2">
                <span className={`inline-block size-2 rounded-full ${connectionStatus ? "bg-green-500" : "bg-red-500"}`} />
                <span>{connectionStatus ? "Device Connected" : "Device Disconnected"}</span>
                {deviceInfo && (
                  <span className="text-muted-foreground">({deviceInfo.name} - {deviceInfo.host}:{deviceInfo.port})</span>
                )}
              </div>
            </header>
            <div className="relative flex-1 overflow-hidden">
              {/* Floating controls */}
              <div className="pointer-events-none absolute inset-0 z-10">
                <div className="pointer-events-auto absolute left-4 top-4 flex gap-2">
                  <button
                    onClick={handleCapture}
                    disabled={!connectionStatus}
                    className="rounded-md bg-emerald-600 text-white px-3 py-2 text-sm shadow hover:bg-emerald-500 disabled:opacity-50"
                  >
                    Capture
                  </button>
                </div>
                <div className="pointer-events-auto absolute right-4 top-4 flex gap-2">
                  <button
                    onClick={handleSend}
                    disabled={!connectionStatus}
                    className="rounded-md bg-amber-600 text-white px-3 py-2 text-sm shadow hover:bg-amber-500 disabled:opacity-50"
                  >
                    Send
                  </button>
                </div>
                <div className="pointer-events-auto absolute left-4 bottom-4">
                  <ImagePreview images={images} />
                </div>
              </div>

              {/* Chat thread */}
              <Thread />
            </div>
          </SidebarInset>
        </div>
      </SidebarProvider>
    </AssistantRuntimeProvider>
  );
};
