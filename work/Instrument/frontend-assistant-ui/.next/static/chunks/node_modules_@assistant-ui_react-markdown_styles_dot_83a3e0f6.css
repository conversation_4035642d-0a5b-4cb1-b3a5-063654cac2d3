/* [project]/node_modules/@assistant-ui/react-markdown/styles/dot.css [app-client] (css) */
@keyframes aui-pulse {
  50% {
    opacity: .5;
  }
}

:where(.aui-md[data-status="running"]):empty:after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:not(ol):not(ul):not(pre)):last-child:after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > pre:last-child code:after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:is(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:is(ol, ul):last-child) > :where(li:last-child) > :where(:is(ol, ul):last-child) > :where(li:last-child:not(:has(* > li))):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child) > :where(:-webkit-any(ol, ul):last-child) > :where(li:last-child):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child) > :where(:-moz-any(ol, ul):last-child) > :where(li:last-child):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

:where(.aui-md[data-status="running"]) > :where(:is(ol, ul):last-child) > :where(li:last-child) > :where(:is(ol, ul):last-child) > :where(li:last-child) > :where(:is(ol, ul):last-child) > :where(li:last-child):after {
  --aui-content: "●";
  content: var(--aui-content);
  margin-left: .25rem;
  margin-right: .25rem;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Noto Sans, Ubuntu, Cantarell, Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite aui-pulse;
}

/*# sourceMappingURL=node_modules_%40assistant-ui_react-markdown_styles_dot_83a3e0f6.css.map*/