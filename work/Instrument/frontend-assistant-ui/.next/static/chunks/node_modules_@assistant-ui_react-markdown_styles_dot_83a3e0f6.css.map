{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/node_modules/%40assistant-ui/react-markdown/styles/dot.css"], "sourcesContent": ["@keyframes aui-pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n\n:where(.aui-md[data-status=\"running\"]):empty::after,\n:where(.aui-md[data-status=\"running\"])\n  > :where(:not(ol):not(ul):not(pre)):last-child::after,\n:where(.aui-md[data-status=\"running\"]) > pre:last-child code::after,\n:where(.aui-md[data-status=\"running\"])\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child:not(:has(* > li)))::after,\n:where(.aui-md[data-status=\"running\"])\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child)\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child:not(:has(* > li)))::after,\n:where(.aui-md[data-status=\"running\"])\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child)\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child)\n  > :where(:is(ol, ul):last-child)\n  > :where(li:last-child)::after {\n  animation: aui-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n  font-family:\n    ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\",\n    \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  --aui-content: \"\\25cf\";\n  content: var(--aui-content);\n  margin-left: 0.25rem;\n  margin-right: 0.25rem;\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA;;;;;;;;;AAAA", "ignoreList": [0], "debugId": null}}]}