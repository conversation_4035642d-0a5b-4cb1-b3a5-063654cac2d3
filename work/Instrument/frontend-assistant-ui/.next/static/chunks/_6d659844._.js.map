{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\n\nimport { cn } from \"@/lib/utils\";\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  );\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  );\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  );\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,KAEoC;QAFpC,EACf,GAAG,OACgD,GAFpC;IAGf,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,KAEgC;QAFhC,EACtB,GAAG,OACmD,GAFhC;IAGtB,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,KAKgC;QALhC,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD,GALhC;IAMtB,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tooltip-icon-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ComponentPropsWithoutRef, forwardRef } from \"react\";\n\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\n\nexport type TooltipIconButtonProps = ComponentPropsWithoutRef<typeof Button> & {\n  tooltip: string;\n  side?: \"top\" | \"bottom\" | \"left\" | \"right\";\n};\n\nexport const TooltipIconButton = forwardRef<\n  HTMLButtonElement,\n  TooltipIconButtonProps\n>(({ children, tooltip, side = \"bottom\", className, ...rest }, ref) => {\n  return (\n    <TooltipProvider>\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            {...rest}\n            className={cn(\"size-6 p-1\", className)}\n            ref={ref}\n          >\n            {children}\n            <span className=\"sr-only\">{tooltip}</span>\n          </Button>\n        </TooltipTrigger>\n        <TooltipContent side={side}>{tooltip}</TooltipContent>\n      </Tooltip>\n    </TooltipProvider>\n  );\n});\n\nTooltipIconButton.displayName = \"TooltipIconButton\";\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAMA;AACA;AAXA;;;;;;AAkBO,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAGxC,QAA6D;QAA5D,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM;IAC3D,qBACE,6LAAC,+HAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8BACN,6LAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACJ,GAAG,IAAI;wBACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;wBAC5B,KAAK;;4BAEJ;0CACD,6LAAC;gCAAK,WAAU;0CAAW;;;;;;;;;;;;;;;;;8BAG/B,6LAAC,+HAAA,CAAA,iBAAc;oBAAC,MAAM;8BAAO;;;;;;;;;;;;;;;;;AAIrC;;AAEA,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/markdown-text.tsx"], "sourcesContent": ["\"use client\";\n\nimport \"@assistant-ui/react-markdown/styles/dot.css\";\n\nimport {\n  CodeHeaderProps,\n  MarkdownTextPrimitive,\n  unstable_memoizeMarkdownComponents as memoizeMarkdownComponents,\n  useIsMarkdownCodeBlock,\n} from \"@assistant-ui/react-markdown\";\nimport remarkGfm from \"remark-gfm\";\nimport { FC, memo, useState } from \"react\";\nimport { CheckIcon, CopyIcon } from \"lucide-react\";\n\nimport { TooltipIconButton } from \"@/components/assistant-ui/tooltip-icon-button\";\nimport { cn } from \"@/lib/utils\";\n\nconst MarkdownTextImpl = () => {\n  return (\n    <MarkdownTextPrimitive\n      remarkPlugins={[remarkGfm]}\n      className=\"aui-md\"\n      components={defaultComponents}\n    />\n  );\n};\n\nexport const MarkdownText = memo(MarkdownTextImpl);\n\nconst CodeHeader: FC<CodeHeaderProps> = ({ language, code }) => {\n  const { isCopied, copyToClipboard } = useCopyToClipboard();\n  const onCopy = () => {\n    if (!code || isCopied) return;\n    copyToClipboard(code);\n  };\n\n  return (\n    <div className=\"flex items-center justify-between gap-4 rounded-t-lg bg-zinc-900 px-4 py-2 text-sm font-semibold text-white\">\n      <span className=\"lowercase [&>span]:text-xs\">{language}</span>\n      <TooltipIconButton tooltip=\"Copy\" onClick={onCopy}>\n        {!isCopied && <CopyIcon />}\n        {isCopied && <CheckIcon />}\n      </TooltipIconButton>\n    </div>\n  );\n};\n\nconst useCopyToClipboard = ({\n  copiedDuration = 3000,\n}: {\n  copiedDuration?: number;\n} = {}) => {\n  const [isCopied, setIsCopied] = useState<boolean>(false);\n\n  const copyToClipboard = (value: string) => {\n    if (!value) return;\n\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n      setTimeout(() => setIsCopied(false), copiedDuration);\n    });\n  };\n\n  return { isCopied, copyToClipboard };\n};\n\nconst defaultComponents = memoizeMarkdownComponents({\n  h1: ({ className, ...props }) => (\n    <h1 className={cn(\"mb-8 scroll-m-20 text-4xl font-extrabold tracking-tight last:mb-0\", className)} {...props} />\n  ),\n  h2: ({ className, ...props }) => (\n    <h2 className={cn(\"mb-4 mt-8 scroll-m-20 text-3xl font-semibold tracking-tight first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  h3: ({ className, ...props }) => (\n    <h3 className={cn(\"mb-4 mt-6 scroll-m-20 text-2xl font-semibold tracking-tight first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  h4: ({ className, ...props }) => (\n    <h4 className={cn(\"mb-4 mt-6 scroll-m-20 text-xl font-semibold tracking-tight first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  h5: ({ className, ...props }) => (\n    <h5 className={cn(\"my-4 text-lg font-semibold first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  h6: ({ className, ...props }) => (\n    <h6 className={cn(\"my-4 font-semibold first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  p: ({ className, ...props }) => (\n    <p className={cn(\"mb-5 mt-5 leading-7 first:mt-0 last:mb-0\", className)} {...props} />\n  ),\n  a: ({ className, ...props }) => (\n    <a className={cn(\"text-primary font-medium underline underline-offset-4\", className)} {...props} />\n  ),\n  blockquote: ({ className, ...props }) => (\n    <blockquote className={cn(\"border-l-2 pl-6 italic\", className)} {...props} />\n  ),\n  ul: ({ className, ...props }) => (\n    <ul className={cn(\"my-5 ml-6 list-disc [&>li]:mt-2\", className)} {...props} />\n  ),\n  ol: ({ className, ...props }) => (\n    <ol className={cn(\"my-5 ml-6 list-decimal [&>li]:mt-2\", className)} {...props} />\n  ),\n  hr: ({ className, ...props }) => (\n    <hr className={cn(\"my-5 border-b\", className)} {...props} />\n  ),\n  table: ({ className, ...props }) => (\n    <table className={cn(\"my-5 w-full border-separate border-spacing-0 overflow-y-auto\", className)} {...props} />\n  ),\n  th: ({ className, ...props }) => (\n    <th className={cn(\"bg-muted px-4 py-2 text-left font-bold first:rounded-tl-lg last:rounded-tr-lg [&[align=center]]:text-center [&[align=right]]:text-right\", className)} {...props} />\n  ),\n  td: ({ className, ...props }) => (\n    <td className={cn(\"border-b border-l px-4 py-2 text-left last:border-r [&[align=center]]:text-center [&[align=right]]:text-right\", className)} {...props} />\n  ),\n  tr: ({ className, ...props }) => (\n    <tr className={cn(\"m-0 border-b p-0 first:border-t [&:last-child>td:first-child]:rounded-bl-lg [&:last-child>td:last-child]:rounded-br-lg\", className)} {...props} />\n  ),\n  sup: ({ className, ...props }) => (\n    <sup className={cn(\"[&>a]:text-xs [&>a]:no-underline\", className)} {...props} />\n  ),\n  pre: ({ className, ...props }) => (\n    <pre className={cn(\"overflow-x-auto rounded-b-lg bg-black p-4 text-white\", className)} {...props} />\n  ),\n  code: function Code({ className, ...props }) {\n    const isCodeBlock = useIsMarkdownCodeBlock();\n    return (\n      <code\n        className={cn(!isCodeBlock && \"bg-muted rounded border font-semibold\", className)}\n        {...props}\n      />\n    );\n  },\n  CodeHeader,\n});\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAMA;AACA;AACA;AAAA;AAEA;AACA;;;AAfA;;;;;;;;AAiBA,MAAM,mBAAmB;IACvB,qBACE,6LAAC,+LAAA,CAAA,wBAAqB;QACpB,eAAe;YAAC,gJAAA,CAAA,UAAS;SAAC;QAC1B,WAAU;QACV,YAAY;;;;;;AAGlB;KARM;AAUC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;;AAEjC,MAAM,aAAkC;QAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;;IACzD,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;IACtC,MAAM,SAAS;QACb,IAAI,CAAC,QAAQ,UAAU;QACvB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;0BAA8B;;;;;;0BAC9C,6LAAC,8JAAA,CAAA,oBAAiB;gBAAC,SAAQ;gBAAO,SAAS;;oBACxC,CAAC,0BAAY,6LAAC,yMAAA,CAAA,WAAQ;;;;;oBACtB,0BAAY,6LAAC,2MAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;AAI/B;GAhBM;;QACkC;;;MADlC;AAkBN,MAAM,qBAAqB;QAAC,EAC1B,iBAAiB,IAAI,EAGtB,oEAAG,CAAC;;IACH,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,OAAO;QAEZ,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC;YACxC,YAAY;YACZ,WAAW,IAAM,YAAY,QAAQ;QACvC;IACF;IAEA,OAAO;QAAE;QAAU;IAAgB;AACrC;IAjBM;AAmBN,MAAM,oBAAoB,CAAA,GAAA,mQAAA,CAAA,qCAAyB,AAAD,EAAE;IAClD,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qEAAqE;YAAa,GAAG,KAAK;;;;;;;IAE9G,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oFAAoF;YAAa,GAAG,KAAK;;;;;;;IAE7H,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oFAAoF;YAAa,GAAG,KAAK;;;;;;;IAE7H,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mFAAmF;YAAa,GAAG,KAAK;;;;;;;IAE5H,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mDAAmD;YAAa,GAAG,KAAK;;;;;;;IAE5F,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;YAAa,GAAG,KAAK;;;;;;;IAEpF,GAAG;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BACzB,6LAAC;YAAE,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;YAAa,GAAG,KAAK;;;;;;;IAEpF,GAAG;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BACzB,6LAAC;YAAE,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;YAAa,GAAG,KAAK;;;;;;;IAEjG,YAAY;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAClC,6LAAC;YAAW,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAa,GAAG,KAAK;;;;;;;IAE3E,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;YAAa,GAAG,KAAK;;;;;;;IAE5E,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;YAAa,GAAG,KAAK;;;;;;;IAE/E,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;YAAa,GAAG,KAAK;;;;;;;IAE1D,OAAO;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC7B,6LAAC;YAAM,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;YAAa,GAAG,KAAK;;;;;;;IAE5G,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2IAA2I;YAAa,GAAG,KAAK;;;;;;;IAEpL,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iHAAiH;YAAa,GAAG,KAAK;;;;;;;IAE1J,IAAI;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC1B,6LAAC;YAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0HAA0H;YAAa,GAAG,KAAK;;;;;;;IAEnK,KAAK;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC3B,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;YAAa,GAAG,KAAK;;;;;;;IAE9E,KAAK;YAAC,EAAE,SAAS,EAAE,GAAG,OAAO;6BAC3B,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;YAAa,GAAG,KAAK;;;;;;;IAElG,IAAI,MAAE,SAAS,KAAK,KAAuB;YAAvB,EAAE,SAAS,EAAE,GAAG,OAAO,GAAvB;;QAClB,MAAM,cAAc,CAAA,GAAA,6LAAA,CAAA,yBAAsB,AAAD;QACzC,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAC,eAAe,yCAAyC;YACtE,GAAG,KAAK;;;;;;IAGf;;YAPsB,6LAAA,CAAA,yBAAsB;;;IAQ5C;AACF", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/tool-fallback.tsx"], "sourcesContent": ["import { ToolCallContentPartComponent } from \"@assistant-ui/react\";\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"../ui/button\";\n\nexport const ToolFallback: ToolCallContentPartComponent = ({\n  toolName,\n  argsText,\n  result,\n}) => {\n  const [isCollapsed, setIsCollapsed] = useState(true);\n  return (\n    <div className=\"mb-4 flex w-full flex-col gap-3 rounded-lg border py-3\">\n      <div className=\"flex items-center gap-2 px-4\">\n        <CheckIcon className=\"size-4\" />\n        <p className=\"\">\n          Used tool: <b>{toolName}</b>\n        </p>\n        <div className=\"flex-grow\" />\n        <Button onClick={() => setIsCollapsed(!isCollapsed)}>\n          {isCollapsed ? <ChevronUpIcon /> : <ChevronDownIcon />}\n        </Button>\n      </div>\n      {!isCollapsed && (\n        <div className=\"flex flex-col gap-2 border-t pt-2\">\n          <div className=\"px-4\">\n            <pre className=\"whitespace-pre-wrap\">{argsText}</pre>\n          </div>\n          {result !== undefined && (\n            <div className=\"border-t border-dashed px-4 pt-2\">\n              <p className=\"font-semibold\">Result:</p>\n              <pre className=\"whitespace-pre-wrap\">\n                {typeof result === \"string\"\n                  ? result\n                  : JSON.stringify(result, null, 2)}\n              </pre>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEO,MAAM,eAA6C;QAAC,EACzD,QAAQ,EACR,QAAQ,EACR,MAAM,EACP;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAE,WAAU;;4BAAG;0CACH,6LAAC;0CAAG;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,eAAe,CAAC;kCACpC,4BAAc,6LAAC,uNAAA,CAAA,gBAAa;;;;qFAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;YAGtD,CAAC,6BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAuB;;;;;;;;;;;oBAEvC,WAAW,2BACV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAC7B,6LAAC;gCAAI,WAAU;0CACZ,OAAO,WAAW,WACf,SACA,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GArCa;KAAA", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread.tsx"], "sourcesContent": ["import {\n  ThreadPrimitive,\n  ComposerPrimitive,\n  MessagePrimitive,\n  ActionBarPrimitive,\n  BranchPickerPrimitive,\n  ErrorPrimitive,\n  ThreadListPrimitive,\n} from \"@assistant-ui/react\";\nimport type { FC } from \"react\";\nimport {\n  ArrowDownIcon,\n  ArrowUpIcon,\n  PlusIcon,\n  CopyIcon,\n  CheckIcon,\n  PencilIcon,\n  RefreshCwIcon,\n  ChevronLeftIcon,\n  ChevronRightIcon,\n  Square,\n} from \"lucide-react\";\n\nimport { TooltipIconButton } from \"@/components/assistant-ui/tooltip-icon-button\";\nimport { motion } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\nimport { MarkdownText } from \"./markdown-text\";\nimport { ToolFallback } from \"./tool-fallback\";\n\nexport const Thread: FC = () => {\n  return (\n    <ThreadPrimitive.Root\n      // aui-thread-root\n      className=\"bg-background flex h-full flex-col\"\n      style={{\n        [\"--thread-max-width\" as string]: \"48rem\",\n        [\"--thread-padding-x\" as string]: \"1rem\",\n      }}\n    >\n      {/* aui-thread-viewport */}\n      <ThreadPrimitive.Viewport className=\"relative flex min-w-0 flex-1 flex-col gap-6 overflow-y-scroll\">\n        {/* Top toolbar with New Chat and Stop */}\n        <div className=\"sticky top-2 z-10 mx-auto flex w-full max-w-[var(--thread-max-width)] justify-end gap-2 px-[var(--thread-padding-x)]\">\n          <ThreadListPrimitive.New asChild>\n            <Button variant=\"outline\" size=\"sm\" aria-label=\"New Chat\">New Chat</Button>\n          </ThreadListPrimitive.New>\n          <ThreadPrimitive.If running>\n            <ComposerPrimitive.Cancel asChild>\n              <Button variant=\"destructive\" size=\"sm\" aria-label=\"Stop generating\">Stop</Button>\n            </ComposerPrimitive.Cancel>\n          </ThreadPrimitive.If>\n        </div>\n\n        <ThreadWelcome />\n\n        <ThreadPrimitive.Messages\n          components={{\n            UserMessage,\n            EditComposer,\n            AssistantMessage,\n          }}\n        />\n\n        <ThreadPrimitive.If empty={false}>\n          {/* aui-thread-viewport-spacer */}\n          <motion.div className=\"min-h-6 min-w-6 shrink-0\" />\n        </ThreadPrimitive.If>\n      </ThreadPrimitive.Viewport>\n\n      <Composer />\n    </ThreadPrimitive.Root>\n  );\n};\n\nconst ThreadScrollToBottom: FC = () => {\n  return (\n    <ThreadPrimitive.ScrollToBottom asChild>\n      <TooltipIconButton\n        tooltip=\"Scroll to bottom\"\n        variant=\"outline\"\n        // aui-thread-scroll-to-bottom\n        className=\"dark:bg-background dark:hover:bg-accent absolute -top-12 z-10 self-center rounded-full p-4 disabled:invisible\"\n      >\n        <ArrowDownIcon />\n      </TooltipIconButton>\n    </ThreadPrimitive.ScrollToBottom>\n  );\n};\n\nconst ThreadWelcome: FC = () => {\n  return (\n    <ThreadPrimitive.Empty>\n      {/* aui-thread-welcome-root */}\n      <div className=\"mx-auto flex w-full max-w-[var(--thread-max-width)] flex-grow flex-col px-[var(--thread-padding-x)]\">\n        {/* aui-thread-welcome-center */}\n        <div className=\"flex w-full flex-grow flex-col items-center justify-center\">\n          {/* aui-thread-welcome-message */}\n          <div className=\"flex size-full flex-col justify-center px-8 md:mt-20\">\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: 10 }}\n              transition={{ delay: 0.5 }}\n              // aui-thread-welcome-message-motion-1\n              className=\"text-2xl font-semibold\"\n            >\n              Hello there!\n            </motion.div>\n            <motion.div\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: 10 }}\n              transition={{ delay: 0.6 }}\n              // aui-thread-welcome-message-motion-2\n              className=\"text-muted-foreground/65 text-2xl\"\n            >\n              How can I help you today?\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </ThreadPrimitive.Empty>\n  );\n};\n\nconst ThreadWelcomeSuggestions: FC = () => {\n  return (\n    // aui-thread-welcome-suggestions\n    <div className=\"grid w-full gap-2 sm:grid-cols-2\">\n      {[\n        {\n          title: \"What are the advantages\",\n          label: \"of using Assistant Cloud?\",\n          action: \"What are the advantages of using Assistant Cloud?\",\n        },\n        {\n          title: \"Write code to\",\n          label: `demonstrate topological sorting`,\n          action: `Write code to demonstrate topological sorting`,\n        },\n        {\n          title: \"Help me write an essay\",\n          label: `about AI chat applications`,\n          action: `Help me write an essay about AI chat applications`,\n        },\n        {\n          title: \"What is the weather\",\n          label: \"in San Francisco?\",\n          action: \"What is the weather in San Francisco?\",\n        },\n      ].map((suggestedAction, index) => (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          transition={{ delay: 0.05 * index }}\n          key={`suggested-action-${suggestedAction.title}-${index}`}\n          // aui-thread-welcome-suggestion-display\n          className=\"[&:nth-child(n+3)]:hidden sm:[&:nth-child(n+3)]:block\"\n        >\n          <ThreadPrimitive.Suggestion\n            prompt={suggestedAction.action}\n            method=\"replace\"\n            autoSend\n            asChild\n          >\n            <Button\n              variant=\"ghost\"\n              // aui-thread-welcome-suggestion\n              className=\"dark:hover:bg-accent/60 h-auto w-full flex-1 flex-wrap items-start justify-start gap-1 rounded-xl border px-4 py-3.5 text-left text-sm sm:flex-col\"\n              aria-label={suggestedAction.action}\n            >\n              {/* aui-thread-welcome-suggestion-text-1 */}\n              <span className=\"font-medium\">{suggestedAction.title}</span>\n              {/* aui-thread-welcome-suggestion-text-2 */}\n              <p className=\"text-muted-foreground\">{suggestedAction.label}</p>\n            </Button>\n          </ThreadPrimitive.Suggestion>\n        </motion.div>\n      ))}\n    </div>\n  );\n};\n\nconst Composer: FC = () => {\n  return (\n    // aui-composer-wrapper\n    <div className=\"bg-background relative mx-auto flex w-full max-w-[var(--thread-max-width)] flex-col gap-4 px-[var(--thread-padding-x)] pb-4 md:pb-6\">\n      <ThreadScrollToBottom />\n      <ThreadPrimitive.Empty>\n        <ThreadWelcomeSuggestions />\n      </ThreadPrimitive.Empty>\n      {/* aui-composer-root */}\n      <ComposerPrimitive.Root className=\"focus-within::ring-offset-2 relative flex w-full flex-col rounded-2xl focus-within:ring-2 focus-within:ring-black dark:focus-within:ring-white\">\n        {/* aui-composer-input */}\n        <ComposerPrimitive.Input\n          placeholder=\"Send a message...\"\n          className={\n            \"bg-muted border-border dark:border-muted-foreground/15 focus:outline-primary placeholder:text-muted-foreground max-h-[calc(50dvh)] min-h-16 w-full resize-none rounded-t-2xl border-x border-t px-4 pt-2 pb-3 text-base outline-none\"\n          }\n          rows={1}\n          autoFocus\n          aria-label=\"Message input\"\n        />\n        <ComposerAction />\n      </ComposerPrimitive.Root>\n    </div>\n  );\n};\n\nconst ComposerAction: FC = () => {\n  return (\n    // aui-composer-action-wrapper\n    <div className=\"bg-muted border-border dark:border-muted-foreground/15 relative flex items-center justify-between rounded-b-2xl border-x border-b p-2\">\n      <TooltipIconButton\n        tooltip=\"Attach file\"\n        variant=\"ghost\"\n        // aui-composer-attachment-button\n        className=\"hover:bg-foreground/15 dark:hover:bg-background/50 scale-115 p-3.5\"\n        onClick={() => {\n          console.log(\"Attachment clicked - not implemented\");\n        }}\n      >\n        <PlusIcon />\n      </TooltipIconButton>\n\n      <ThreadPrimitive.If running={false}>\n        <ComposerPrimitive.Send asChild>\n          <Button\n            type=\"submit\"\n            variant=\"default\"\n            // aui-composer-send\n            className=\"dark:border-muted-foreground/90 border-muted-foreground/60 hover:bg-primary/75 size-8 rounded-full border\"\n            aria-label=\"Send message\"\n          >\n            {/* aui-composer-send-icon */}\n            <ArrowUpIcon className=\"size-5\" />\n          </Button>\n        </ComposerPrimitive.Send>\n      </ThreadPrimitive.If>\n\n      <ThreadPrimitive.If running>\n        <ComposerPrimitive.Cancel asChild>\n          <Button\n            type=\"button\"\n            variant=\"default\"\n            // aui-composer-cancel\n            className=\"dark:border-muted-foreground/90 border-muted-foreground/60 hover:bg-primary/75 size-8 rounded-full border\"\n            aria-label=\"Stop generating\"\n          >\n            {/* aui-composer-cancel-icon */}\n            <Square className=\"size-3.5 fill-white dark:size-4 dark:fill-black\" />\n          </Button>\n        </ComposerPrimitive.Cancel>\n      </ThreadPrimitive.If>\n    </div>\n  );\n};\n\nconst MessageError: FC = () => {\n  return (\n    <MessagePrimitive.Error>\n      {/* aui-message-error-root */}\n      <ErrorPrimitive.Root className=\"border-destructive bg-destructive/10 dark:bg-destructive/5 text-destructive mt-2 rounded-md border p-3 text-sm dark:text-red-200\">\n        {/* aui-message-error-message */}\n        <ErrorPrimitive.Message className=\"line-clamp-2\" />\n      </ErrorPrimitive.Root>\n    </MessagePrimitive.Error>\n  );\n};\n\nconst AssistantMessage: FC = () => {\n  return (\n    <MessagePrimitive.Root asChild>\n      <motion.div\n        // aui-assistant-message-root\n        className=\"relative mx-auto grid w-full max-w-[var(--thread-max-width)] grid-cols-[auto_auto_1fr] grid-rows-[auto_1fr] px-[var(--thread-padding-x)] py-4\"\n        initial={{ y: 5, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        data-role=\"assistant\"\n      >\n        {/* aui-assistant-message-avatar */}\n        <div className=\"ring-border bg-background col-start-1 row-start-1 flex size-8 shrink-0 items-center justify-center rounded-full ring-1\">\n          <StarIcon size={14} />\n        </div>\n\n        {/* aui-assistant-message-content */}\n        <div className=\"text-foreground col-span-2 col-start-2 row-start-1 ml-4 leading-7 break-words\">\n          <MessagePrimitive.Content\n            components={{\n              Text: MarkdownText,\n              tools: { Fallback: ToolFallback },\n            }}\n          />\n          <MessageError />\n        </div>\n\n        <AssistantActionBar />\n\n        {/* aui-assistant-branch-picker */}\n        <BranchPicker className=\"col-start-2 row-start-2 mr-2 -ml-2\" />\n      </motion.div>\n    </MessagePrimitive.Root>\n  );\n};\n\nconst AssistantActionBar: FC = () => {\n  return (\n    <ActionBarPrimitive.Root\n      hideWhenRunning\n      autohide=\"not-last\"\n      autohideFloat=\"single-branch\"\n      // aui-assistant-action-bar-root\n      className=\"text-muted-foreground data-floating:bg-background col-start-3 row-start-2 mt-3 ml-3 flex gap-1 data-floating:absolute data-floating:mt-2 data-floating:rounded-md data-floating:border data-floating:p-1 data-floating:shadow-sm\"\n    >\n      <ActionBarPrimitive.Copy asChild>\n        <TooltipIconButton tooltip=\"Copy\">\n          <MessagePrimitive.If copied>\n            <CheckIcon />\n          </MessagePrimitive.If>\n          <MessagePrimitive.If copied={false}>\n            <CopyIcon />\n          </MessagePrimitive.If>\n        </TooltipIconButton>\n      </ActionBarPrimitive.Copy>\n      <ActionBarPrimitive.Reload asChild>\n        <TooltipIconButton tooltip=\"Refresh\">\n          <RefreshCwIcon />\n        </TooltipIconButton>\n      </ActionBarPrimitive.Reload>\n    </ActionBarPrimitive.Root>\n  );\n};\n\nconst UserMessage: FC = () => {\n  return (\n    <MessagePrimitive.Root asChild>\n      <motion.div\n        // aui-user-message-root\n        className=\"mx-auto grid w-full max-w-[var(--thread-max-width)] auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-1 px-[var(--thread-padding-x)] py-4 [&:where(>*)]:col-start-2\"\n        initial={{ y: 5, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        data-role=\"user\"\n      >\n        <UserActionBar />\n\n        {/* aui-user-message-content */}\n        <div className=\"bg-muted text-foreground col-start-2 rounded-3xl px-5 py-2.5 break-words\">\n          <MessagePrimitive.Content components={{ Text: MarkdownText }} />\n        </div>\n\n        {/* aui-user-branch-picker */}\n        <BranchPicker className=\"col-span-full col-start-1 row-start-3 -mr-1 justify-end\" />\n      </motion.div>\n    </MessagePrimitive.Root>\n  );\n};\n\nconst UserActionBar: FC = () => {\n  return (\n    <ActionBarPrimitive.Root\n      hideWhenRunning\n      autohide=\"not-last\"\n      // aui-user-action-bar-root\n      className=\"col-start-1 mt-2.5 mr-3 flex flex-col items-end\"\n    >\n      <ActionBarPrimitive.Edit asChild>\n        <TooltipIconButton tooltip=\"Edit\">\n          <PencilIcon />\n        </TooltipIconButton>\n      </ActionBarPrimitive.Edit>\n    </ActionBarPrimitive.Root>\n  );\n};\n\nconst EditComposer: FC = () => {\n  return (\n    // aui-edit-composer-wrapper\n    <div className=\"mx-auto flex w-full max-w-[var(--thread-max-width)] flex-col gap-4 px-[var(--thread-padding-x)]\">\n      {/* aui-edit-composer-root */}\n      <ComposerPrimitive.Root className=\"bg-muted ml-auto flex w-full max-w-7/8 flex-col rounded-xl\">\n        {/* aui-edit-composer-input */}\n        <ComposerPrimitive.Input\n          className=\"text-foreground flex min-h-[60px] w-full resize-none bg-transparent p-4 outline-none\"\n          autoFocus\n        />\n\n        {/* aui-edit-composer-footer */}\n        <div className=\"mx-3 mb-3 flex items-center justify-center gap-2 self-end\">\n          <ComposerPrimitive.Cancel asChild>\n            <Button variant=\"ghost\" size=\"sm\" aria-label=\"Cancel edit\">\n              Cancel\n            </Button>\n          </ComposerPrimitive.Cancel>\n          <ComposerPrimitive.Send asChild>\n            <Button size=\"sm\" aria-label=\"Update message\">\n              Update\n            </Button>\n          </ComposerPrimitive.Send>\n        </div>\n      </ComposerPrimitive.Root>\n    </div>\n  );\n};\n\nconst BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({\n  className,\n  ...rest\n}) => {\n  return (\n    <BranchPickerPrimitive.Root\n      hideWhenSingleBranch\n      // aui-branch-picker-root\n      className={cn(\n        \"text-muted-foreground inline-flex items-center text-xs\",\n        className,\n      )}\n      {...rest}\n    >\n      <BranchPickerPrimitive.Previous asChild>\n        <TooltipIconButton tooltip=\"Previous\">\n          <ChevronLeftIcon />\n        </TooltipIconButton>\n      </BranchPickerPrimitive.Previous>\n      {/* aui-branch-picker-state */}\n      <span className=\"font-medium\">\n        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />\n      </span>\n      <BranchPickerPrimitive.Next asChild>\n        <TooltipIconButton tooltip=\"Next\">\n          <ChevronRightIcon />\n        </TooltipIconButton>\n      </BranchPickerPrimitive.Next>\n    </BranchPickerPrimitive.Root>\n  );\n};\n\nconst StarIcon = ({ size = 14 }: { size?: number }) => (\n  <svg\n    width={size}\n    height={size}\n    viewBox=\"0 0 16 16\"\n    fill=\"none\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <path\n      d=\"M8 0L9.79611 6.20389L16 8L9.79611 9.79611L8 16L6.20389 9.79611L0 8L6.20389 6.20389L8 0Z\"\n      fill=\"currentColor\"\n    />\n  </svg>\n);\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,SAAa;IACxB,qBACE,6LAAC,iOAAA,CAAA,kBAAe,CAAC,IAAI;QACnB,kBAAkB;QAClB,WAAU;QACV,OAAO;YACL,CAAC,qBAA+B,EAAE;YAClC,CAAC,qBAA+B,EAAE;QACpC;;0BAGA,6LAAC,iOAAA,CAAA,kBAAe,CAAC,QAAQ;gBAAC,WAAU;;kCAElC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yOAAA,CAAA,sBAAmB,CAAC,GAAG;gCAAC,OAAO;0CAC9B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,cAAW;8CAAW;;;;;;;;;;;0CAE5D,6LAAC,iOAAA,CAAA,kBAAe,CAAC,EAAE;gCAAC,OAAO;0CACzB,cAAA,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,MAAM;oCAAC,OAAO;8CAC/B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAc,MAAK;wCAAK,cAAW;kDAAkB;;;;;;;;;;;;;;;;;;;;;;kCAK3E,6LAAC;;;;;kCAED,6LAAC,iOAAA,CAAA,kBAAe,CAAC,QAAQ;wBACvB,YAAY;4BACV;4BACA;4BACA;wBACF;;;;;;kCAGF,6LAAC,iOAAA,CAAA,kBAAe,CAAC,EAAE;wBAAC,OAAO;kCAEzB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI1B,6LAAC;;;;;;;;;;;AAGP;KA3Ca;AA6Cb,MAAM,uBAA2B;IAC/B,qBACE,6LAAC,iOAAA,CAAA,kBAAe,CAAC,cAAc;QAAC,OAAO;kBACrC,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;YAChB,SAAQ;YACR,SAAQ;YACR,8BAA8B;YAC9B,WAAU;sBAEV,cAAA,6LAAC,uNAAA,CAAA,gBAAa;;;;;;;;;;;;;;;AAItB;MAbM;AAeN,MAAM,gBAAoB;IACxB,qBACE,6LAAC,iOAAA,CAAA,kBAAe,CAAC,KAAK;kBAEpB,cAAA,6LAAC;YAAI,WAAU;sBAEb,cAAA,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC1B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,sCAAsC;4BACtC,WAAU;sCACX;;;;;;sCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC1B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,sCAAsC;4BACtC,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;MAlCM;AAoCN,MAAM,2BAA+B;IACnC,OACE,iCAAiC;kBACjC,6LAAC;QAAI,WAAU;kBACZ;YACC;gBACE,OAAO;gBACP,OAAO;gBACP,QAAQ;YACV;YACA;gBACE,OAAO;gBACP,OAAQ;gBACR,QAAS;YACX;YACA;gBACE,OAAO;gBACP,OAAQ;gBACR,QAAS;YACX;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,QAAQ;YACV;SACD,CAAC,GAAG,CAAC,CAAC,iBAAiB,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC1B,YAAY;oBAAE,OAAO,OAAO;gBAAM;gBAElC,wCAAwC;gBACxC,WAAU;0BAEV,cAAA,6LAAC,iOAAA,CAAA,kBAAe,CAAC,UAAU;oBACzB,QAAQ,gBAAgB,MAAM;oBAC9B,QAAO;oBACP,QAAQ;oBACR,OAAO;8BAEP,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,gCAAgC;wBAChC,WAAU;wBACV,cAAY,gBAAgB,MAAM;;0CAGlC,6LAAC;gCAAK,WAAU;0CAAe,gBAAgB,KAAK;;;;;;0CAEpD,6LAAC;gCAAE,WAAU;0CAAyB,gBAAgB,KAAK;;;;;;;;;;;;;;;;;eAnB1D,AAAC,oBAA4C,OAAzB,gBAAgB,KAAK,EAAC,KAAS,OAAN;;;;;;;;;;AA0B5D;MAzDM;AA2DN,MAAM,WAAe;IACnB,OACE,uBAAuB;kBACvB,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;;;;0BACD,6LAAC,iOAAA,CAAA,kBAAe,CAAC,KAAK;0BACpB,cAAA,6LAAC;;;;;;;;;;0BAGH,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,IAAI;gBAAC,WAAU;;kCAEhC,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,KAAK;wBACtB,aAAY;wBACZ,WACE;wBAEF,MAAM;wBACN,SAAS;wBACT,cAAW;;;;;;kCAEb,6LAAC;;;;;;;;;;;;;;;;;AAIT;MAxBM;AA0BN,MAAM,iBAAqB;IACzB,OACE,8BAA8B;kBAC9B,6LAAC;QAAI,WAAU;;0BACb,6LAAC,8JAAA,CAAA,oBAAiB;gBAChB,SAAQ;gBACR,SAAQ;gBACR,iCAAiC;gBACjC,WAAU;gBACV,SAAS;oBACP,QAAQ,GAAG,CAAC;gBACd;0BAEA,cAAA,6LAAC,yMAAA,CAAA,WAAQ;;;;;;;;;;0BAGX,6LAAC,iOAAA,CAAA,kBAAe,CAAC,EAAE;gBAAC,SAAS;0BAC3B,cAAA,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,IAAI;oBAAC,OAAO;8BAC7B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,oBAAoB;wBACpB,WAAU;wBACV,cAAW;kCAGX,cAAA,6LAAC,mNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC,iOAAA,CAAA,kBAAe,CAAC,EAAE;gBAAC,OAAO;0BACzB,cAAA,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,MAAM;oBAAC,OAAO;8BAC/B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,sBAAsB;wBACtB,WAAU;wBACV,cAAW;kCAGX,cAAA,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9B;MA/CM;AAiDN,MAAM,eAAmB;IACvB,qBACE,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,KAAK;kBAErB,cAAA,6LAAC,+NAAA,CAAA,iBAAc,CAAC,IAAI;YAAC,WAAU;sBAE7B,cAAA,6LAAC,+NAAA,CAAA,iBAAc,CAAC,OAAO;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1C;MAVM;AAYN,MAAM,mBAAuB;IAC3B,qBACE,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,IAAI;QAAC,OAAO;kBAC5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,6BAA6B;YAC7B,WAAU;YACV,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,aAAU;;8BAGV,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAS,MAAM;;;;;;;;;;;8BAIlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,OAAO;4BACvB,YAAY;gCACV,MAAM,qJAAA,CAAA,eAAY;gCAClB,OAAO;oCAAE,UAAU,qJAAA,CAAA,eAAY;gCAAC;4BAClC;;;;;;sCAEF,6LAAC;;;;;;;;;;;8BAGH,6LAAC;;;;;8BAGD,6LAAC;oBAAa,WAAU;;;;;;;;;;;;;;;;;AAIhC;MAjCM;AAmCN,MAAM,qBAAyB;IAC7B,qBACE,6LAAC,uOAAA,CAAA,qBAAkB,CAAC,IAAI;QACtB,eAAe;QACf,UAAS;QACT,eAAc;QACd,gCAAgC;QAChC,WAAU;;0BAEV,6LAAC,uOAAA,CAAA,qBAAkB,CAAC,IAAI;gBAAC,OAAO;0BAC9B,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;oBAAC,SAAQ;;sCACzB,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,EAAE;4BAAC,MAAM;sCACzB,cAAA,6LAAC,2MAAA,CAAA,YAAS;;;;;;;;;;sCAEZ,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,EAAE;4BAAC,QAAQ;sCAC3B,cAAA,6LAAC,yMAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;0BAIf,6LAAC,uOAAA,CAAA,qBAAkB,CAAC,MAAM;gBAAC,OAAO;0BAChC,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;oBAAC,SAAQ;8BACzB,cAAA,6LAAC,uNAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;AAKxB;MA1BM;AA4BN,MAAM,cAAkB;IACtB,qBACE,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,IAAI;QAAC,OAAO;kBAC5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,wBAAwB;YACxB,WAAU;YACV,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,aAAU;;8BAEV,6LAAC;;;;;8BAGD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mOAAA,CAAA,mBAAgB,CAAC,OAAO;wBAAC,YAAY;4BAAE,MAAM,qJAAA,CAAA,eAAY;wBAAC;;;;;;;;;;;8BAI7D,6LAAC;oBAAa,WAAU;;;;;;;;;;;;;;;;;AAIhC;MAtBM;AAwBN,MAAM,gBAAoB;IACxB,qBACE,6LAAC,uOAAA,CAAA,qBAAkB,CAAC,IAAI;QACtB,eAAe;QACf,UAAS;QACT,2BAA2B;QAC3B,WAAU;kBAEV,cAAA,6LAAC,uOAAA,CAAA,qBAAkB,CAAC,IAAI;YAAC,OAAO;sBAC9B,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;gBAAC,SAAQ;0BACzB,cAAA,6LAAC,6MAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;AAKrB;OAfM;AAiBN,MAAM,eAAmB;IACvB,OACE,4BAA4B;kBAC5B,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,IAAI;YAAC,WAAU;;8BAEhC,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,KAAK;oBACtB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,MAAM;4BAAC,OAAO;sCAC/B,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,cAAW;0CAAc;;;;;;;;;;;sCAI7D,6LAAC,qOAAA,CAAA,oBAAiB,CAAC,IAAI;4BAAC,OAAO;sCAC7B,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,cAAW;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;OA5BM;AA8BN,MAAM,eAAqD;QAAC,EAC1D,SAAS,EACT,GAAG,MACJ;IACC,qBACE,6LAAC,6OAAA,CAAA,wBAAqB,CAAC,IAAI;QACzB,oBAAoB;QACpB,yBAAyB;QACzB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,IAAI;;0BAER,6LAAC,6OAAA,CAAA,wBAAqB,CAAC,QAAQ;gBAAC,OAAO;0BACrC,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;oBAAC,SAAQ;8BACzB,cAAA,6LAAC,2NAAA,CAAA,kBAAe;;;;;;;;;;;;;;;0BAIpB,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,6OAAA,CAAA,wBAAqB,CAAC,MAAM;;;;;oBAAG;kCAAG,6LAAC,6OAAA,CAAA,wBAAqB,CAAC,KAAK;;;;;;;;;;;0BAEjE,6LAAC,6OAAA,CAAA,wBAAqB,CAAC,IAAI;gBAAC,OAAO;0BACjC,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;oBAAC,SAAQ;8BACzB,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;AAK3B;OA9BM;AAgCN,MAAM,WAAW;QAAC,EAAE,OAAO,EAAE,EAAqB;yBAChD,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,6LAAC;YACC,GAAE;YACF,MAAK;;;;;;;;;;;;OAVL", "debugId": null}}, {"offset": {"line": 1714, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,WAAc,CAAsB;IAEpE,6JAAA,CAAA,YAAe;iCAAC;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,AAAC,eAAoC,OAAtB,oBAAoB,GAAE;YACnE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator-root\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 1821, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2027, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"skeleton\"\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"size-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,6JAAA,CAAA,gBAAmB,CAA6B;AAEvE,SAAS;;IACP,MAAM,UAAU,6JAAA,CAAA,aAAgB,CAAC;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,KAYxB;QAZwB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ,GAZwB;;IAavB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,WAAc,CAAC;IACzC,MAAM,OAAO,qBAAA,sBAAA,WAAY;IACzB,MAAM,UAAU,6JAAA,CAAA,cAAiB;gDAC/B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,AAAC,GAAyB,OAAvB,qBAAoB,KAAiC,OAA9B,WAAU,sBAA2C,OAAvB;QAC5E;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,6JAAA,CAAA,cAAiB;sDAAC;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,6JAAA,CAAA,YAAe;qCAAC;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,6JAAA,CAAA,UAAa;iDAChC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,+HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,yHAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,KAWhB;QAXgB,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ,GAXgB;;IAYf,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,6HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,6HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,6HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,6HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,KAIc;QAJd,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC,GAJd;;IAKtB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,8HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,oBAAA,8BAAA,QAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,KAAuD;QAAvD,EAAE,SAAS,EAAE,GAAG,OAAuC,GAAvD;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAGe;QAHf,EACpB,SAAS,EACT,GAAG,OACgC,GAHf;IAIpB,qBACE,6LAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACrB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,KAGe;QAHf,EACxB,SAAS,EACT,GAAG,OACoC,GAHf;IAIxB,qBACE,6LAAC,iIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,KAI2B;QAJ3B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD,GAJ3B;IAKzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,KAI6B;QAJ7B,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD,GAJ7B;IAK1B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,KAGC;QAHD,EAC3B,SAAS,EACT,GAAG,OACyB,GAHD;IAI3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,KAYuB;QAZvB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C,GAZvB;;IAazB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAO;;0BACN,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,+HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;IAhDS;;QAcqB;;;OAdrB;AAkDT,SAAS,kBAAkB,KAQ1B;QAR0B,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ,GAR0B;IASzB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS;AAgCT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OApBS;AAsBT,SAAS,oBAAoB,KAM5B;QAN4B,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ,GAN4B;;IAO3B,kCAAkC;IAClC,MAAM,QAAQ,6JAAA,CAAA,UAAa;8CAAC;YAC1B,OAAO,AAAC,GAAsC,OAApC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,IAAG;QAChD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,gIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,KAGC;QAHD,EAC1B,SAAS,EACT,GAAG,OACwB,GAHD;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,KAU7B;QAV6B,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ,GAV6B;IAW5B,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OA9BS", "debugId": null}}, {"offset": {"line": 2834, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/thread-list.tsx"], "sourcesContent": ["import type { FC } from \"react\";\nimport {\n  ThreadListItemPrimitive,\n  ThreadListPrimitive,\n} from \"@assistant-ui/react\";\nimport { ArchiveIcon, PlusIcon } from \"lucide-react\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { TooltipIconButton } from \"@/components/assistant-ui/tooltip-icon-button\";\n\nexport const ThreadList: FC = () => {\n  return (\n    <ThreadListPrimitive.Root className=\"flex flex-col items-stretch gap-1.5\">\n      <ThreadListNew />\n      <ThreadListItems />\n    </ThreadListPrimitive.Root>\n  );\n};\n\nconst ThreadListNew: FC = () => {\n  return (\n    <ThreadListPrimitive.New asChild>\n      <Button className=\"data-active:bg-muted hover:bg-muted flex items-center justify-start gap-1 rounded-lg px-2.5 py-2 text-start\" variant=\"ghost\">\n        <PlusIcon />\n        New Thread\n      </Button>\n    </ThreadListPrimitive.New>\n  );\n};\n\nconst ThreadListItems: FC = () => {\n  return <ThreadListPrimitive.Items components={{ ThreadListItem }} />;\n};\n\nconst ThreadListItem: FC = () => {\n  return (\n    <ThreadListItemPrimitive.Root className=\"data-active:bg-muted hover:bg-muted focus-visible:bg-muted focus-visible:ring-ring flex items-center gap-2 rounded-lg transition-all focus-visible:outline-none focus-visible:ring-2\">\n      <ThreadListItemPrimitive.Trigger className=\"flex-grow px-3 py-2 text-start\">\n        <ThreadListItemTitle />\n      </ThreadListItemPrimitive.Trigger>\n      <ThreadListItemArchive />\n    </ThreadListItemPrimitive.Root>\n  );\n};\n\nconst ThreadListItemTitle: FC = () => {\n  return (\n    <p className=\"text-sm\">\n      <ThreadListItemPrimitive.Title fallback=\"New Chat\" />\n    </p>\n  );\n};\n\nconst ThreadListItemArchive: FC = () => {\n  return (\n    <ThreadListItemPrimitive.Archive asChild>\n      <TooltipIconButton\n        className=\"hover:text-foreground/60 p-4 text-foreground ml-auto mr-1 size-4\"\n        variant=\"ghost\"\n        tooltip=\"Archive thread\"\n      >\n        <ArchiveIcon />\n      </TooltipIconButton>\n    </ThreadListItemPrimitive.Archive>\n  );\n};\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAIA;AAAA;AAEA;AACA;;;;;;AAEO,MAAM,aAAiB;IAC5B,qBACE,6LAAC,yOAAA,CAAA,sBAAmB,CAAC,IAAI;QAAC,WAAU;;0BAClC,6LAAC;;;;;0BACD,6LAAC;;;;;;;;;;;AAGP;KAPa;AASb,MAAM,gBAAoB;IACxB,qBACE,6LAAC,yOAAA,CAAA,sBAAmB,CAAC,GAAG;QAAC,OAAO;kBAC9B,cAAA,6LAAC,8HAAA,CAAA,SAAM;YAAC,WAAU;YAA8G,SAAQ;;8BACtI,6LAAC,yMAAA,CAAA,WAAQ;;;;;gBAAG;;;;;;;;;;;;AAKpB;MATM;AAWN,MAAM,kBAAsB;IAC1B,qBAAO,6LAAC,yOAAA,CAAA,sBAAmB,CAAC,KAAK;QAAC,YAAY;YAAE;QAAe;;;;;;AACjE;MAFM;AAIN,MAAM,iBAAqB;IACzB,qBACE,6LAAC,iPAAA,CAAA,0BAAuB,CAAC,IAAI;QAAC,WAAU;;0BACtC,6LAAC,iPAAA,CAAA,0BAAuB,CAAC,OAAO;gBAAC,WAAU;0BACzC,cAAA,6LAAC;;;;;;;;;;0BAEH,6LAAC;;;;;;;;;;;AAGP;MATM;AAWN,MAAM,sBAA0B;IAC9B,qBACE,6LAAC;QAAE,WAAU;kBACX,cAAA,6LAAC,iPAAA,CAAA,0BAAuB,CAAC,KAAK;YAAC,UAAS;;;;;;;;;;;AAG9C;MANM;AAQN,MAAM,wBAA4B;IAChC,qBACE,6LAAC,iPAAA,CAAA,0BAAuB,CAAC,OAAO;QAAC,OAAO;kBACtC,cAAA,6LAAC,8JAAA,CAAA,oBAAiB;YAChB,WAAU;YACV,SAAQ;YACR,SAAQ;sBAER,cAAA,6LAAC,+MAAA,CAAA,cAAW;;;;;;;;;;;;;;;AAIpB;MAZM", "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/app-sidebar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Gith<PERSON>, MessagesSquare } from \"lucide-react\"\nimport Link from \"next/link\"\nimport {\n  Sidebar,\n  SidebarContent,\n  Sidebar<PERSON>ooter,\n  SidebarHeader,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarRail,\n} from \"@/components/ui/sidebar\"\nimport { ThreadList } from \"./assistant-ui/thread-list\"\n\nexport function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {\n  return (\n    <Sidebar {...props}>\n      <SidebarHeader>\n        <SidebarMenu>\n          <SidebarMenuItem>\n            <SidebarMenuButton size=\"lg\" asChild>\n                <Link href=\"https://assistant-ui.com\" target=\"_blank\">\n                  <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground\">\n                    <MessagesSquare className=\"size-4\" />\n                  </div>\n                  <div className=\"flex flex-col gap-0.5 leading-none\">\n                    <span className=\"font-semibold\">assistant-ui</span>\n                  </div>\n                </Link>\n              </SidebarMenuButton>\n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarHeader>\n      <SidebarContent>\n        <ThreadList />\n      </SidebarContent>\n      \n      <SidebarRail />\n      <SidebarFooter>\n        <SidebarMenu>\n         \n          <SidebarMenuItem>\n            <SidebarMenuButton size=\"lg\" asChild>\n              <Link href=\"https://github.com/assistant-ui/assistant-ui\" target=\"_blank\">\n                <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground\">\n                  <Github className=\"size-4\" />\n                </div>\n                <div className=\"flex flex-col gap-0.5 leading-none\">\n                  <span className=\"font-semibold\">GitHub</span>\n                  <span className=\"\">View Source</span>\n                </div>\n              </Link>\n            </SidebarMenuButton>\n            \n          </SidebarMenuItem>\n        </SidebarMenu>\n      </SidebarFooter>\n    </Sidebar>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AAUA;;;;;;AAEO,SAAS,WAAW,KAAkD;QAAlD,EAAE,GAAG,OAA6C,GAAlD;IACzB,qBACE,6LAAC,+HAAA,CAAA,UAAO;QAAE,GAAG,KAAK;;0BAChB,6LAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,+HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;4BAAC,MAAK;4BAAK,OAAO;sCAChC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,QAAO;;kDAC3C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC,+HAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,mJAAA,CAAA,aAAU;;;;;;;;;;0BAGb,6LAAC,+HAAA,CAAA,cAAW;;;;;0BACZ,6LAAC,+HAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC,+HAAA,CAAA,cAAW;8BAEV,cAAA,6LAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,+HAAA,CAAA,oBAAiB;4BAAC,MAAK;4BAAK,OAAO;sCAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA+C,QAAO;;kDAC/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC;KA7CgB", "debugId": null}}, {"offset": {"line": 3189, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\n}\n\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\n  return (\n    <ol\n      data-slot=\"breadcrumb-list\"\n      className={cn(\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-item\"\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbLink({\n  asChild,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"breadcrumb-link\"\n      className={cn(\"hover:text-foreground transition-colors\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-page\"\n      role=\"link\"\n      aria-disabled=\"true\"\n      aria-current=\"page\"\n      className={cn(\"text-foreground font-normal\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction BreadcrumbSeparator({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"breadcrumb-separator\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"[&>svg]:size-3.5\", className)}\n      {...props}\n    >\n      {children ?? <ChevronRight />}\n    </li>\n  )\n}\n\nfunction BreadcrumbEllipsis({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"breadcrumb-ellipsis\"\n      role=\"presentation\"\n      aria-hidden=\"true\"\n      className={cn(\"flex size-9 items-center justify-center\", className)}\n      {...props}\n    >\n      <MoreHorizontal className=\"size-4\" />\n      <span className=\"sr-only\">More</span>\n    </span>\n  )\n}\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,KAAyC;QAAzC,EAAE,GAAG,OAAoC,GAAzC;IAClB,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,KAMvB;QANuB,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ,GANuB;IAOtB,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,KAAqD;QAArD,EAAE,SAAS,EAAE,GAAG,OAAqC,GAArD;IACtB,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,KAIA;QAJA,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,GAJA;IAK3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,qBAAA,sBAAA,yBAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,KAGG;QAHH,EAC1B,SAAS,EACT,GAAG,OAC0B,GAHH;IAI1B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 3346, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/lib/instrument-runtime.ts"], "sourcesContent": ["import type {\n  <PERSON>t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,\n  ChatModelRunResult\n} from \"@assistant-ui/react\";\n\nexport interface DeviceInfo {\n  name: string;\n  host: string;\n  port: number;\n}\n\nexport interface InstrumentRuntimeConfig {\n  backendUrl: string;\n  wsUrl: string;\n  onDeviceStatusChange?: (connected: boolean, deviceInfo: DeviceInfo | null) => void;\n  onImagesReceived?: (images: string[]) => void;\n}\n\nexport class InstrumentManager {\n  private ws: WebSocket | null = null;\n  private config: InstrumentRuntimeConfig;\n  private deviceConnected = false;\n  private deviceInfo: DeviceInfo | null = null;\n  private currentImages: string[] = [];\n\n  constructor(config: InstrumentRuntimeConfig) {\n    this.config = config;\n    this.connectWebSocket();\n  }\n\n  private connectWebSocket() {\n    try {\n      this.ws = new WebSocket(this.config.wsUrl);\n\n      this.ws.onopen = () => {\n        console.log('Connected to backend WebSocket');\n      };\n\n      this.ws.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          this.handleWebSocketMessage(data);\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      this.ws.onclose = () => {\n        console.log('WebSocket connection closed');\n        // Only attempt to reconnect if not manually closed\n        if (this.ws) {\n          setTimeout(() => this.connectWebSocket(), 3000);\n        }\n      };\n\n      this.ws.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        // Don't spam reconnection attempts on error\n      };\n    } catch (error) {\n      console.error('Failed to connect WebSocket:', error);\n      // Retry connection after delay on initialization failure\n      setTimeout(() => this.connectWebSocket(), 5000);\n    }\n  }\n\n  private handleWebSocketMessage(data: any) {\n    switch (data.type) {\n      case 'device_status':\n        this.deviceConnected = data.connected;\n        this.deviceInfo = data.deviceInfo;\n        this.config.onDeviceStatusChange?.(this.deviceConnected, this.deviceInfo);\n        break;\n\n      case 'images':\n        if (data.images && Array.isArray(data.images)) {\n          this.currentImages = data.images;\n          this.config.onImagesReceived?.(data.images);\n          console.log(`Received ${data.images.length} images`);\n        }\n        break;\n\n      case 'error':\n        console.error('Device error:', data.message);\n        break;\n\n      default:\n        console.log('Unknown message type:', data.type);\n    }\n  }\n\n  // Send command to device via WebSocket\n  public sendDeviceCommand(command: string, params: any = {}) {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      this.ws.send(JSON.stringify({\n        type: command,\n        ...params\n      }));\n    } else {\n      throw new Error('Not connected to backend');\n    }\n  }\n\n  // Capture images from device\n  public captureImages(count: number = 1) {\n    this.sendDeviceCommand('capture', { count });\n  }\n\n  // Send command to device\n  public sendToDevice() {\n    this.sendDeviceCommand('send');\n  }\n\n  // Get current device status\n  public getDeviceStatus() {\n    return {\n      connected: this.deviceConnected,\n      deviceInfo: this.deviceInfo\n    };\n  }\n\n  // Get current images\n  public getCurrentImages() {\n    return this.currentImages;\n  }\n\n  // Create the ChatModelAdapter for assistant-ui\n  public createAdapter(): ChatModelAdapter {\n    return {\n      async *run({ messages, abortSignal }) {\n        try {\n          // Extract images from the last user message\n          const lastUserMessage = messages.findLast(m => m.role === \"user\");\n          const images: string[] = [];\n          let context = \"\";\n\n          if (lastUserMessage) {\n            if (typeof lastUserMessage.content === \"string\") {\n              context = lastUserMessage.content;\n            } else if (Array.isArray(lastUserMessage.content)) {\n              for (const part of lastUserMessage.content) {\n                if (part.type === \"text\") {\n                  context += part.text;\n                } else if (part.type === \"image\") {\n                  // Extract base64 from data URL\n                  const base64 = part.image.replace(/^data:image\\/[a-z]+;base64,/, '');\n                  images.push(base64);\n                }\n              }\n            }\n          }\n\n          // If no images in message content, use current images\n          const imagesToSend = images.length > 0 ? images : (this.currentImages || []);\n\n          if (!imagesToSend || imagesToSend.length === 0) {\n            // Provide a helpful response when no images are available\n            yield {\n              content: [\n                {\n                  type: \"text\",\n                  text: \"No images are available for analysis. Please capture some images first using the 'Capture' button, or ensure your device is connected.\"\n                }\n              ]\n            };\n            return;\n          }\n\n          // Make request to backend\n          let response;\n          try {\n            response = await fetch(`${this.config.backendUrl}/api/chat`, {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json',\n              },\n              body: JSON.stringify({\n                images: imagesToSend,\n                context: context.trim()\n              }),\n              signal: abortSignal\n            });\n          } catch (fetchError) {\n            // Handle network errors (backend not available)\n            yield {\n              content: [\n                {\n                  type: \"text\",\n                  text: `Unable to connect to the backend server at ${this.config.backendUrl}. Please ensure the backend is running and accessible.`\n                }\n              ]\n            };\n            return;\n          }\n\n          if (!response.ok) {\n            yield {\n              content: [\n                {\n                  type: \"text\",\n                  text: `Backend server error (${response.status}): ${response.statusText}. Please check the backend logs.`\n                }\n              ]\n            };\n            return;\n          }\n\n          // Handle streaming response\n          const reader = response.body?.getReader();\n          const decoder = new TextDecoder();\n\n          if (!reader) {\n            throw new Error('No response body');\n          }\n\n          let fullText = \"\";\n\n          while (true) {\n            const { done, value } = await reader.read();\n\n            if (done) break;\n\n            const chunk = decoder.decode(value);\n            const lines = chunk.split('\\n');\n\n            for (const line of lines) {\n              if (line.startsWith('data: ')) {\n                const data = line.slice(6);\n\n                if (data === '[DONE]') {\n                  return;\n                }\n\n                try {\n                  const parsed = JSON.parse(data);\n                  if (parsed.text) {\n                    fullText += parsed.text;\n                    // Yield the current state\n                    yield {\n                      content: [{ type: \"text\", text: fullText }]\n                    } as ChatModelRunResult;\n                  } else if (parsed.error) {\n                    throw new Error(parsed.error);\n                  }\n                } catch (parseError) {\n                  // Ignore parsing errors for partial chunks\n                }\n              }\n            }\n          }\n        } catch (error) {\n          console.error('Analysis error:', error);\n          // Provide a user-friendly error message instead of throwing\n          yield {\n            content: [\n              {\n                type: \"text\",\n                text: `An error occurred during analysis: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`\n              }\n            ]\n          };\n        }\n      }\n    };\n  }\n\n  // Clean up WebSocket connection\n  public destroy() {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n}\n\n"], "names": [], "mappings": ";;;;;AAkBO,MAAM;IAYH,mBAAmB;QACzB,IAAI;YACF,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK;YAEzC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;gBACf,QAAQ,GAAG,CAAC;YACd;YAEA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;gBACnB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;oBAClC,IAAI,CAAC,sBAAsB,CAAC;gBAC9B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;gBACpD;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG;gBAChB,QAAQ,GAAG,CAAC;gBACZ,mDAAmD;gBACnD,IAAI,IAAI,CAAC,EAAE,EAAE;oBACX,WAAW,IAAM,IAAI,CAAC,gBAAgB,IAAI;gBAC5C;YACF;YAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;gBACjB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,4CAA4C;YAC9C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,yDAAyD;YACzD,WAAW,IAAM,IAAI,CAAC,gBAAgB,IAAI;QAC5C;IACF;IAEQ,uBAAuB,IAAS,EAAE;QACxC,OAAQ,KAAK,IAAI;YACf,KAAK;oBAGH,mCAAA;gBAFA,IAAI,CAAC,eAAe,GAAG,KAAK,SAAS;gBACrC,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;iBACjC,oCAAA,CAAA,eAAA,IAAI,CAAC,MAAM,EAAC,oBAAoB,cAAhC,wDAAA,uCAAA,cAAmC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU;gBACxE;YAEF,KAAK;gBACH,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;wBAE7C,+BAAA;oBADA,IAAI,CAAC,aAAa,GAAG,KAAK,MAAM;qBAChC,gCAAA,CAAA,gBAAA,IAAI,CAAC,MAAM,EAAC,gBAAgB,cAA5B,oDAAA,mCAAA,eAA+B,KAAK,MAAM;oBAC1C,QAAQ,GAAG,CAAC,AAAC,YAA8B,OAAnB,KAAK,MAAM,CAAC,MAAM,EAAC;gBAC7C;gBACA;YAEF,KAAK;gBACH,QAAQ,KAAK,CAAC,iBAAiB,KAAK,OAAO;gBAC3C;YAEF;gBACE,QAAQ,GAAG,CAAC,yBAAyB,KAAK,IAAI;QAClD;IACF;IAEA,uCAAuC;IAChC,kBAAkB,OAAe,EAAoB;YAAlB,SAAA,iEAAc,CAAC;QACvD,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACpD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;gBAC1B,MAAM;gBACN,GAAG,MAAM;YACX;QACF,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,6BAA6B;IACtB,gBAAiC;YAAnB,QAAA,iEAAgB;QACnC,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAAE;QAAM;IAC5C;IAEA,yBAAyB;IAClB,eAAe;QACpB,IAAI,CAAC,iBAAiB,CAAC;IACzB;IAEA,4BAA4B;IACrB,kBAAkB;QACvB,OAAO;YACL,WAAW,IAAI,CAAC,eAAe;YAC/B,YAAY,IAAI,CAAC,UAAU;QAC7B;IACF;IAEA,qBAAqB;IACd,mBAAmB;QACxB,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,+CAA+C;IACxC,gBAAkC;QACvC,OAAO;YACL,OAAO,KAAI,KAAyB;oBAAzB,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAzB;gBACT,IAAI;wBA8Ea;oBA7Ef,4CAA4C;oBAC5C,MAAM,kBAAkB,SAAS,QAAQ,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;oBAC1D,MAAM,SAAmB,EAAE;oBAC3B,IAAI,UAAU;oBAEd,IAAI,iBAAiB;wBACnB,IAAI,OAAO,gBAAgB,OAAO,KAAK,UAAU;4BAC/C,UAAU,gBAAgB,OAAO;wBACnC,OAAO,IAAI,MAAM,OAAO,CAAC,gBAAgB,OAAO,GAAG;4BACjD,KAAK,MAAM,QAAQ,gBAAgB,OAAO,CAAE;gCAC1C,IAAI,KAAK,IAAI,KAAK,QAAQ;oCACxB,WAAW,KAAK,IAAI;gCACtB,OAAO,IAAI,KAAK,IAAI,KAAK,SAAS;oCAChC,+BAA+B;oCAC/B,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,+BAA+B;oCACjE,OAAO,IAAI,CAAC;gCACd;4BACF;wBACF;oBACF;oBAEA,sDAAsD;oBACtD,MAAM,eAAe,OAAO,MAAM,GAAG,IAAI,SAAU,IAAI,CAAC,aAAa,IAAI,EAAE;oBAE3E,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;wBAC9C,0DAA0D;wBAC1D,MAAM;4BACJ,SAAS;gCACP;oCACE,MAAM;oCACN,MAAM;gCACR;6BACD;wBACH;wBACA;oBACF;oBAEA,0BAA0B;oBAC1B,IAAI;oBACJ,IAAI;wBACF,WAAW,MAAM,MAAM,AAAC,GAAyB,OAAvB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAC,cAAY;4BAC3D,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;gCACnB,QAAQ;gCACR,SAAS,QAAQ,IAAI;4BACvB;4BACA,QAAQ;wBACV;oBACF,EAAE,OAAO,YAAY;wBACnB,gDAAgD;wBAChD,MAAM;4BACJ,SAAS;gCACP;oCACE,MAAM;oCACN,MAAM,AAAC,8CAAoE,OAAvB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAC;gCAC7E;6BACD;wBACH;wBACA;oBACF;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM;4BACJ,SAAS;gCACP;oCACE,MAAM;oCACN,MAAM,AAAC,yBAA6C,OAArB,SAAS,MAAM,EAAC,OAAyB,OAApB,SAAS,UAAU,EAAC;gCAC1E;6BACD;wBACH;wBACA;oBACF;oBAEA,4BAA4B;oBAC5B,MAAM,UAAS,iBAAA,SAAS,IAAI,cAAb,qCAAA,eAAe,SAAS;oBACvC,MAAM,UAAU,IAAI;oBAEpB,IAAI,CAAC,QAAQ;wBACX,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI,WAAW;oBAEf,MAAO,KAAM;wBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;wBAEzC,IAAI,MAAM;wBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;wBAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;wBAE1B,KAAK,MAAM,QAAQ,MAAO;4BACxB,IAAI,KAAK,UAAU,CAAC,WAAW;gCAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;gCAExB,IAAI,SAAS,UAAU;oCACrB;gCACF;gCAEA,IAAI;oCACF,MAAM,SAAS,KAAK,KAAK,CAAC;oCAC1B,IAAI,OAAO,IAAI,EAAE;wCACf,YAAY,OAAO,IAAI;wCACvB,0BAA0B;wCAC1B,MAAM;4CACJ,SAAS;gDAAC;oDAAE,MAAM;oDAAQ,MAAM;gDAAS;6CAAE;wCAC7C;oCACF,OAAO,IAAI,OAAO,KAAK,EAAE;wCACvB,MAAM,IAAI,MAAM,OAAO,KAAK;oCAC9B;gCACF,EAAE,OAAO,YAAY;gCACnB,2CAA2C;gCAC7C;4BACF;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mBAAmB;oBACjC,4DAA4D;oBAC5D,MAAM;wBACJ,SAAS;4BACP;gCACE,MAAM;gCACN,MAAM,AAAC,sCAA8F,OAAzD,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAgB;4BACvG;yBACD;oBACH;gBACF;YACF;QACF;IACF;IAEA,gCAAgC;IACzB,UAAU;QACf,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;IAvPA,YAAY,MAA+B,CAAE;QAN7C,+KAAQ,MAAuB;QAC/B,+KAAQ,UAAR,KAAA;QACA,+KAAQ,mBAAkB;QAC1B,+KAAQ,cAAgC;QACxC,+KAAQ,iBAA0B,EAAE;QAGlC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,gBAAgB;IACvB;AAqPF", "debugId": null}}, {"offset": {"line": 3598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/components/assistant-ui/image-preview.tsx"], "sourcesContent": ["import { FC } from \"react\";\n\ninterface ImagePreviewProps {\n  images: string[];\n  className?: string;\n}\n\nexport const ImagePreview: FC<ImagePreviewProps> = ({ images, className = \"\" }) => {\n  if (images.length === 0) return null;\n\n  return (\n    <div className={`backdrop-blur-md bg-black/70 text-white p-3 rounded-xl shadow-xl ${className}`}>\n      <h3 className=\"m-0 mb-2 text-sm text-slate-300\">Captured Images</h3>\n      <div className=\"flex items-center gap-2\">\n        {images.slice(0, 3).map((image, index) => (\n          <img\n            key={index}\n            src={`data:image/jpeg;base64,${image}`}\n            alt={`Capture ${index + 1}`}\n            className=\"w-14 h-14 object-cover rounded-md border border-white/20\"\n          />\n        ))}\n        {images.length > 3 && (\n          <div className=\"text-slate-300 text-sm px-2\">+{images.length - 3} more</div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,eAAsC;QAAC,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;IAC5E,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,6LAAC;QAAI,WAAW,AAAC,oEAA6E,OAAV;;0BAClF,6LAAC;gBAAG,WAAU;0BAAkC;;;;;;0BAChD,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;4BAEC,KAAK,AAAC,0BAA+B,OAAN;4BAC/B,KAAK,AAAC,WAAoB,OAAV,QAAQ;4BACxB,WAAU;2BAHL;;;;;oBAMR,OAAO,MAAM,GAAG,mBACf,6LAAC;wBAAI,WAAU;;4BAA8B;4BAAE,OAAO,MAAM,GAAG;4BAAE;;;;;;;;;;;;;;;;;;;AAK3E;KArBa", "debugId": null}}, {"offset": {"line": 3666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/work/Instrument/frontend-assistant-ui/app/assistant.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useMemo, useState } from \"react\";\nimport { AssistantRuntimeProvider, useLocalRuntime } from \"@assistant-ui/react\";\nimport { Thread } from \"@/components/assistant-ui/thread\";\nimport {\n  SidebarInset,\n  SidebarProvider,\n  SidebarTrigger,\n} from \"@/components/ui/sidebar\";\nimport { AppSidebar } from \"@/components/app-sidebar\";\nimport { Separator } from \"@/components/ui/separator\";\nimport {\n  Breadcrumb,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbList,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n} from \"@/components/ui/breadcrumb\";\nimport { InstrumentManager, type DeviceInfo } from \"@/lib/instrument-runtime\";\nimport { ImagePreview } from \"@/components/assistant-ui/image-preview\";\n\nexport const Assistant = () => {\n  // Config - using different ports to avoid conflicts with Next.js dev server\n  const BACKEND_URL = \"http://localhost:8080\";\n  const WS_URL = \"ws://localhost:8081\";\n\n  // Instrument state\n  const [connectionStatus, setConnectionStatus] = useState(false);\n  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);\n  const [images, setImages] = useState<string[]>([]);\n  const [instrumentManager, setInstrumentManager] = useState<InstrumentManager | null>(null);\n\n  // Initialize InstrumentManager once\n  useEffect(() => {\n    const manager = new InstrumentManager({\n      backendUrl: BACKEND_URL,\n      wsUrl: WS_URL,\n      onDeviceStatusChange: (connected, info) => {\n        setConnectionStatus(connected);\n        setDeviceInfo(info);\n      },\n      onImagesReceived: (imgs) => setImages(imgs),\n    });\n\n    setInstrumentManager(manager);\n    return () => manager.destroy();\n  }, []);\n\n  // Fallback adapter while initializing\n  const defaultAdapter = useMemo(\n    () => ({\n      async *run() {\n        yield { content: [{ type: \"text\", text: \"Initializing...\" }] } as any;\n      },\n    }),\n    []\n  );\n\n  // Local runtime backed by our Instrument adapter\n  const runtime = useLocalRuntime(\n    instrumentManager ? instrumentManager.createAdapter() : (defaultAdapter as any)\n  );\n\n  // Handlers\n  const handleCapture = () => instrumentManager?.captureImages(1);\n  const handleSend = () => instrumentManager?.sendToDevice();\n\n  return (\n    <AssistantRuntimeProvider runtime={runtime}>\n      <SidebarProvider>\n        <div className=\"flex h-dvh w-full pr-0.5\">\n          <AppSidebar />\n          <SidebarInset>\n            <header className=\"flex h-16 shrink-0 items-center gap-2 border-b px-4\">\n              <SidebarTrigger />\n              <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\n              <Breadcrumb>\n                <BreadcrumbList>\n                  <BreadcrumbItem className=\"hidden md:block\">\n                    <BreadcrumbLink href=\"https://www.assistant-ui.com/docs/getting-started\" target=\"_blank\" rel=\"noopener noreferrer\">\n                      Build Your Own ChatGPT UX\n                    </BreadcrumbLink>\n                  </BreadcrumbItem>\n                  <BreadcrumbSeparator className=\"hidden md:block\" />\n                  <BreadcrumbItem>\n                    <BreadcrumbPage>Starter Template</BreadcrumbPage>\n                  </BreadcrumbItem>\n                </BreadcrumbList>\n              </Breadcrumb>\n\n              {/* Device status chip */}\n              <div className=\"ml-auto rounded-full px-3 py-1 text-sm border flex items-center gap-2\">\n                <span className={`inline-block size-2 rounded-full ${connectionStatus ? \"bg-green-500\" : \"bg-red-500\"}`} />\n                <span>{connectionStatus ? \"Device Connected\" : \"Device Disconnected\"}</span>\n                {deviceInfo && (\n                  <span className=\"text-muted-foreground\">({deviceInfo.name} - {deviceInfo.host}:{deviceInfo.port})</span>\n                )}\n              </div>\n            </header>\n            <div className=\"relative flex-1 overflow-hidden\">\n              {/* Floating controls */}\n              <div className=\"pointer-events-none absolute inset-0 z-10\">\n                <div className=\"pointer-events-auto absolute left-4 top-4 flex gap-2\">\n                  <button\n                    onClick={handleCapture}\n                    disabled={!connectionStatus}\n                    className=\"rounded-md bg-emerald-600 text-white px-3 py-2 text-sm shadow hover:bg-emerald-500 disabled:opacity-50\"\n                  >\n                    Capture\n                  </button>\n                </div>\n                <div className=\"pointer-events-auto absolute right-4 top-4 flex gap-2\">\n                  <button\n                    onClick={handleSend}\n                    disabled={!connectionStatus}\n                    className=\"rounded-md bg-amber-600 text-white px-3 py-2 text-sm shadow hover:bg-amber-500 disabled:opacity-50\"\n                  >\n                    Send\n                  </button>\n                </div>\n                <div className=\"pointer-events-auto absolute left-4 bottom-4\">\n                  <ImagePreview images={images} />\n                </div>\n              </div>\n\n              {/* Chat thread */}\n              <Thread />\n            </div>\n          </SidebarInset>\n        </div>\n      </SidebarProvider>\n    </AssistantRuntimeProvider>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAKA;AACA;AACA;AAQA;AACA;;;AArBA;;;;;;;;;;AAuBO,MAAM,YAAY;;IACvB,4EAA4E;IAC5E,MAAM,cAAc;IACpB,MAAM,SAAS;IAEf,mBAAmB;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAErF,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,UAAU,IAAI,+HAAA,CAAA,oBAAiB,CAAC;gBACpC,YAAY;gBACZ,OAAO;gBACP,oBAAoB;2CAAE,CAAC,WAAW;wBAChC,oBAAoB;wBACpB,cAAc;oBAChB;;gBACA,gBAAgB;2CAAE,CAAC,OAAS,UAAU;;YACxC;YAEA,qBAAqB;YACrB;uCAAO,IAAM,QAAQ,OAAO;;QAC9B;8BAAG,EAAE;IAEL,sCAAsC;IACtC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAC3B,IAAM,CAAC;gBACL,OAAO;oBACL,MAAM;wBAAE,SAAS;4BAAC;gCAAE,MAAM;gCAAQ,MAAM;4BAAkB;yBAAE;oBAAC;gBAC/D;YACF,CAAC;4CACD,EAAE;IAGJ,iDAAiD;IACjD,MAAM,UAAU,CAAA,GAAA,6LAAA,CAAA,kBAAe,AAAD,EAC5B,oBAAoB,kBAAkB,aAAa,KAAM;IAG3D,WAAW;IACX,MAAM,gBAAgB,IAAM,8BAAA,wCAAA,kBAAmB,aAAa,CAAC;IAC7D,MAAM,aAAa,IAAM,8BAAA,wCAAA,kBAAmB,YAAY;IAExD,qBACE,6LAAC,yMAAA,CAAA,2BAAwB;QAAC,SAAS;kBACjC,cAAA,6LAAC,+HAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,aAAU;;;;;kCACX,6LAAC,+HAAA,CAAA,eAAY;;0CACX,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,+HAAA,CAAA,iBAAc;;;;;kDACf,6LAAC,iIAAA,CAAA,YAAS;wCAAC,aAAY;wCAAW,WAAU;;;;;;kDAC5C,6LAAC,kIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,kIAAA,CAAA,iBAAc;;8DACb,6LAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACxB,cAAA,6LAAC,kIAAA,CAAA,iBAAc;wDAAC,MAAK;wDAAoD,QAAO;wDAAS,KAAI;kEAAsB;;;;;;;;;;;8DAIrH,6LAAC,kIAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;8DAC/B,6LAAC,kIAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC,kIAAA,CAAA,iBAAc;kEAAC;;;;;;;;;;;;;;;;;;;;;;kDAMtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAW,AAAC,oCAAoF,OAAjD,mBAAmB,iBAAiB;;;;;;0DACzF,6LAAC;0DAAM,mBAAmB,qBAAqB;;;;;;4CAC9C,4BACC,6LAAC;gDAAK,WAAU;;oDAAwB;oDAAE,WAAW,IAAI;oDAAC;oDAAI,WAAW,IAAI;oDAAC;oDAAE,WAAW,IAAI;oDAAC;;;;;;;;;;;;;;;;;;;0CAItG,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;8DACX;;;;;;;;;;;0DAIH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;8DACX;;;;;;;;;;;0DAIH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qJAAA,CAAA,eAAY;oDAAC,QAAQ;;;;;;;;;;;;;;;;;kDAK1B,6LAAC,2IAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB;GAhHa;;QAsCK,6LAAA,CAAA,kBAAe;;;KAtCpB", "debugId": null}}]}