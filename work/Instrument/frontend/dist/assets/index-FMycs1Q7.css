*{margin:0;padding:0;box-sizing:border-box}html,body{height:100%;width:100%;overflow:hidden;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}#root{height:100vh;width:100vw;margin:0;padding:0}button{border:none;background:none;font-family:inherit;cursor:pointer}textarea{font-family:inherit;border:none;outline:none;resize:none}::selection{background:#3b82f64d}.control-btn,.status-bar,.chat-header{user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none}@keyframes aui-pulse{50%{opacity:.5}}:where(.aui-md[data-status=running]):empty:after,:where(.aui-md[data-status=running])>:where(:not(ol):not(ul):not(pre)):last-child:after,:where(.aui-md[data-status=running])>pre:last-child code:after,:where(.aui-md[data-status=running])>:where(:is(ol,ul):last-child)>:where(li:last-child:not(:has(*>li))):after,:where(.aui-md[data-status=running])>:where(:is(ol,ul):last-child)>:where(li:last-child)>:where(:is(ol,ul):last-child)>:where(li:last-child:not(:has(*>li))):after,:where(.aui-md[data-status=running])>:where(:is(ol,ul):last-child)>:where(li:last-child)>:where(:is(ol,ul):last-child)>:where(li:last-child)>:where(:is(ol,ul):last-child)>:where(li:last-child):after{animation:aui-pulse 2s cubic-bezier(.4,0,.6,1) infinite;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";--aui-content: "●";content:var(--aui-content);margin-left:.25rem;margin-right:.25rem}@layer properties;@layer properties;@layer theme{:root :where(.aui-root),:host :where(.aui-root){--font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";--font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;--color-red-200: oklch(88.5% .062 18.334);--color-red-400: oklch(70.4% .191 22.216);--color-red-500: oklch(63.7% .237 25.331);--color-red-600: oklch(57.7% .245 27.325);--color-red-700: oklch(50.5% .213 27.518);--color-green-400: oklch(79.2% .209 151.711);--color-green-500: oklch(72.3% .219 149.579);--color-green-600: oklch(62.7% .194 149.214);--color-green-700: oklch(52.7% .154 150.069);--color-black: #000;--color-white: #fff;--spacing: .25rem;--text-xs: .75rem;--text-xs--line-height: calc(1 / .75);--text-sm: .875rem;--text-sm--line-height: calc(1.25 / .875);--text-base: 1rem;--text-base--line-height: 1.5 ;--text-2xl: 1.5rem;--text-2xl--line-height: calc(2 / 1.5);--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--radius-md: calc(var(--radius) - 2px);--radius-lg: var(--radius);--radius-xl: .75rem;--radius-2xl: 1rem;--radius-3xl: 1.5rem;--ease-in: cubic-bezier(.4, 0, 1, 1);--animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;--default-transition-duration: .15s;--default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);--default-font-family: var(--font-sans);--default-mono-font-family: var(--font-mono);--color-background: hsl(var(--background));--color-foreground: hsl(var(--foreground));--color-primary: hsl(var(--primary));--color-primary-foreground: hsl(var(--primary-foreground));--color-muted: hsl(var(--muted));--color-muted-foreground: hsl(var(--muted-foreground));--color-accent: hsl(var(--accent));--color-accent-foreground: hsl(var(--accent-foreground));--color-destructive: hsl(var(--destructive));--color-border: hsl(var(--border));--color-input: hsl(var(--input));--color-ring: hsl(var(--ring))}}@layer base{:where(.aui-root) *,:where(.aui-root) :after,:where(.aui-root) :before,:where(.aui-root) ::backdrop,:where(.aui-root) ::file-selector-button{box-sizing:border-box;margin:0;padding:0;border:0 solid}:where(.aui-root) html,:host :where(.aui-root){line-height:1.5;-webkit-text-size-adjust:100%;tab-size:4;font-family:var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings, normal);font-variation-settings:var(--default-font-variation-settings, normal);-webkit-tap-highlight-color:transparent}:where(.aui-root) hr{height:0;color:inherit;border-top-width:1px}:where(.aui-root) abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}:where(.aui-root) h1,:where(.aui-root) h2,:where(.aui-root) h3,:where(.aui-root) h4,:where(.aui-root) h5,:where(.aui-root) h6{font-size:inherit;font-weight:inherit}:where(.aui-root) a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}:where(.aui-root) b,:where(.aui-root) strong{font-weight:bolder}:where(.aui-root) code,:where(.aui-root) kbd,:where(.aui-root) samp,:where(.aui-root) pre{font-family:var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);font-feature-settings:var(--default-mono-font-feature-settings, normal);font-variation-settings:var(--default-mono-font-variation-settings, normal);font-size:1em}:where(.aui-root) small{font-size:80%}:where(.aui-root) sub,:where(.aui-root) sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}:where(.aui-root) sub{bottom:-.25em}:where(.aui-root) sup{top:-.5em}:where(.aui-root) table{text-indent:0;border-color:inherit;border-collapse:collapse}:where(.aui-root) :-moz-focusring{outline:auto}:where(.aui-root) progress{vertical-align:baseline}:where(.aui-root) summary{display:list-item}:where(.aui-root) ol,:where(.aui-root) ul,:where(.aui-root) menu{list-style:none}:where(.aui-root) img,:where(.aui-root) svg,:where(.aui-root) video,:where(.aui-root) canvas,:where(.aui-root) audio,:where(.aui-root) iframe,:where(.aui-root) embed,:where(.aui-root) object{display:block;vertical-align:middle}:where(.aui-root) img,:where(.aui-root) video{max-width:100%;height:auto}:where(.aui-root) button,:where(.aui-root) input,:where(.aui-root) select,:where(.aui-root) optgroup,:where(.aui-root) textarea,:where(.aui-root) ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;border-radius:0;background-color:transparent;opacity:1}:where(.aui-root) :where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(.aui-root) :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}:where(.aui-root) ::file-selector-button{margin-inline-end:4px}:where(.aui-root) ::placeholder{opacity:1}@supports (not (-webkit-appearance: -apple-pay-button)) or (contain-intrinsic-size: 1px){:where(.aui-root) ::placeholder{color:currentcolor}@supports (color: color-mix(in lab,red,red)){:where(.aui-root) ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}:where(.aui-root) textarea{resize:vertical}:where(.aui-root) ::-webkit-search-decoration{-webkit-appearance:none}:where(.aui-root) ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}:where(.aui-root) ::-webkit-datetime-edit{display:inline-flex}:where(.aui-root) ::-webkit-datetime-edit-fields-wrapper{padding:0}:where(.aui-root) ::-webkit-datetime-edit,:where(.aui-root) ::-webkit-datetime-edit-year-field,:where(.aui-root) ::-webkit-datetime-edit-month-field,:where(.aui-root) ::-webkit-datetime-edit-day-field,:where(.aui-root) ::-webkit-datetime-edit-hour-field,:where(.aui-root) ::-webkit-datetime-edit-minute-field,:where(.aui-root) ::-webkit-datetime-edit-second-field,:where(.aui-root) ::-webkit-datetime-edit-millisecond-field,:where(.aui-root) ::-webkit-datetime-edit-meridiem-field{padding-block:0}:where(.aui-root) ::-webkit-calendar-picker-indicator{line-height:1}:where(.aui-root) :-moz-ui-invalid{box-shadow:none}:where(.aui-root) button,:where(.aui-root) input:where([type=button],[type=reset],[type=submit]),:where(.aui-root) ::file-selector-button{appearance:button}:where(.aui-root) ::-webkit-inner-spin-button,:where(.aui-root) ::-webkit-outer-spin-button{height:auto}:where(.aui-root) [hidden]:where(:not([hidden=until-found])){display:none!important}}.aui-root{color:var(--color-foreground)}.aui-root *{border-color:var(--color-border)}.aui-button{display:inline-flex;align-items:center;justify-content:center;gap:calc(var(--spacing) * 2);border-radius:var(--radius-md);font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height));--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium);white-space:nowrap;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}.aui-button:focus-visible{--tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.aui-button:focus-visible{--tw-ring-color: var(--color-ring)}.aui-button:focus-visible{--tw-outline-style: none;outline-style:none}.aui-button:disabled{pointer-events:none}.aui-button:disabled{opacity:50%}.aui-button svg{pointer-events:none}.aui-button svg{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.aui-button svg{flex-shrink:0}.aui-button-primary{background-color:var(--color-primary);color:var(--color-primary-foreground);--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / .1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / .1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover: hover){.aui-button-primary:hover{background-color:color-mix(in srgb,hsl(var(--primary)) 90%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-button-primary:hover{background-color:color-mix(in oklab,var(--color-primary) 90%,transparent)}}}.aui-button-outline{border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-input);background-color:var(--color-background);--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / .1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / .1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}@media (hover: hover){.aui-button-outline:hover{background-color:var(--color-accent)}}@media (hover: hover){.aui-button-outline:hover{color:var(--color-accent-foreground)}}@media (hover: hover){.aui-button-ghost:hover{background-color:var(--color-accent)}}@media (hover: hover){.aui-button-ghost:hover{color:var(--color-accent-foreground)}}.aui-button-medium{height:calc(var(--spacing) * 9);padding-inline:calc(var(--spacing) * 4);padding-block:calc(var(--spacing) * 2)}.aui-button-icon{width:calc(var(--spacing) * 6);height:calc(var(--spacing) * 6);padding:calc(var(--spacing) * 1)}.aui-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.aui-avatar-root{position:relative;display:flex;height:calc(var(--spacing) * 10);width:calc(var(--spacing) * 10);flex-shrink:0;overflow:hidden;border-radius:calc(infinity * 1px)}.aui-avatar-image{aspect-ratio:1 / 1;height:100%;width:100%;object-fit:cover}.aui-avatar-fallback{display:flex;height:100%;width:100%;align-items:center;justify-content:center;border-radius:calc(infinity * 1px);background-color:var(--color-muted)}.aui-tooltip-content{z-index:50;animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none);overflow:hidden;border-radius:var(--radius-md);background-color:var(--color-primary);padding-inline:calc(var(--spacing) * 3);padding-block:calc(var(--spacing) * 1.5);font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height));color:var(--color-primary-foreground);--tw-enter-opacity: 0 ;--tw-enter-opacity: 0;--tw-enter-scale: 95% ;--tw-enter-scale: .95}.aui-tooltip-content[data-side=bottom]{--tw-enter-translate-y: calc(2*var(--spacing)*-1)}.aui-tooltip-content[data-side=left]{--tw-enter-translate-x: calc(2*var(--spacing))}.aui-tooltip-content[data-side=right]{--tw-enter-translate-x: calc(2*var(--spacing)*-1)}.aui-tooltip-content[data-side=top]{--tw-enter-translate-y: calc(2*var(--spacing))}.aui-tooltip-content[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.aui-tooltip-content[data-state=closed]{--tw-exit-opacity: 0 ;--tw-exit-opacity: 0}.aui-tooltip-content[data-state=closed]{--tw-exit-scale: 95% ;--tw-exit-scale: .95}.aui-dialog-overlay{position:fixed;inset:calc(var(--spacing) * 0);z-index:50;background-color:color-mix(in srgb,#000 80%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-dialog-overlay{background-color:color-mix(in oklab,var(--color-black) 80%,transparent)}}.aui-dialog-overlay[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.aui-dialog-overlay[data-state=closed]{--tw-exit-opacity: 0 ;--tw-exit-opacity: 0}.aui-dialog-overlay[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.aui-dialog-overlay[data-state=open]{--tw-enter-opacity: 0 ;--tw-enter-opacity: 0}.aui-dialog-content{position:fixed;top:50%;left:50%;z-index:50}.aui-dialog-content[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.aui-dialog-content[data-state=closed]{--tw-exit-opacity: 0 ;--tw-exit-opacity: 0}.aui-dialog-content[data-state=closed]{--tw-exit-scale: 95% ;--tw-exit-scale: .95}.aui-dialog-content[data-state=closed]{--tw-exit-translate-x: -50% }.aui-dialog-content[data-state=closed]{--tw-exit-translate-y: -48% }.aui-dialog-content[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.aui-dialog-content[data-state=open]{--tw-enter-opacity: 0 ;--tw-enter-opacity: 0}.aui-dialog-content[data-state=open]{--tw-enter-scale: 95% ;--tw-enter-scale: .95}.aui-dialog-content[data-state=open]{--tw-enter-translate-x: -50% }.aui-dialog-content[data-state=open]{--tw-enter-translate-y: -48% }.aui-dialog-content{display:grid;--tw-translate-x: -50%;--tw-translate-y: -50%;translate:var(--tw-translate-x) var(--tw-translate-y);--tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / .1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / .1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-duration: .2s;transition-duration:.2s}.aui-tool-fallback-root{margin-bottom:calc(var(--spacing) * 4);display:flex;width:100%;flex-direction:column;gap:calc(var(--spacing) * 3);border-radius:var(--radius-lg);border-style:var(--tw-border-style);border-width:1px;padding-block:calc(var(--spacing) * 3)}.aui-tool-fallback-header{display:flex;align-items:center;gap:calc(var(--spacing) * 2);padding-inline:calc(var(--spacing) * 4)}.aui-tool-fallback-icon{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.aui-tool-fallback-title{flex-grow:1}.aui-tool-fallback-content{display:flex;flex-direction:column;gap:calc(var(--spacing) * 2);border-top-style:var(--tw-border-style);border-top-width:1px;padding-top:calc(var(--spacing) * 2)}.aui-tool-fallback-args-root{padding-inline:calc(var(--spacing) * 4)}.aui-tool-fallback-args-value{white-space:pre-wrap}.aui-tool-fallback-result-root{border-top-style:var(--tw-border-style);border-top-width:1px;--tw-border-style: dashed;border-style:dashed;padding-inline:calc(var(--spacing) * 4);padding-top:calc(var(--spacing) * 2)}.aui-tool-fallback-result-header{--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.aui-tool-fallback-result-content{white-space:pre-wrap}.aui-thread-root{display:flex;height:100%;flex-direction:column;background-color:var(--color-background)}.aui-thread-viewport{position:relative;display:flex;min-width:calc(var(--spacing) * 0);flex:1;flex-direction:column;gap:calc(var(--spacing) * 6);overflow-y:scroll}.aui-thread-viewport-spacer{min-height:calc(var(--spacing) * 6);min-width:calc(var(--spacing) * 6);flex-shrink:0}.aui-thread-viewport-footer{position:sticky;bottom:calc(var(--spacing) * 0);margin-top:calc(var(--spacing) * 3);display:flex;width:100%;max-width:var(--thread-max-width);flex-direction:column;align-items:center;justify-content:flex-end;border-top-left-radius:var(--radius-lg);border-top-right-radius:var(--radius-lg);background-color:inherit;padding-bottom:calc(var(--spacing) * 4)}.aui-thread-scroll-to-bottom{position:absolute;top:calc(var(--spacing) * -12);z-index:10;align-self:center;border-radius:calc(infinity * 1px);padding:calc(var(--spacing) * 4)}.aui-thread-scroll-to-bottom:disabled{visibility:hidden}.aui-thread-scroll-to-bottom:where(.dark,.dark *){background-color:var(--color-background)}@media (hover: hover){.aui-thread-scroll-to-bottom:where(.dark,.dark *):hover{background-color:var(--color-accent)}}.aui-thread-followup-suggestions{display:flex;min-height:calc(var(--spacing) * 8);align-items:center;justify-content:center;gap:calc(var(--spacing) * 2)}.aui-thread-followup-suggestion{border-radius:calc(infinity * 1px);border-style:var(--tw-border-style);border-width:1px;background-color:var(--color-background);padding-inline:calc(var(--spacing) * 3);padding-block:calc(var(--spacing) * 1);font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height));transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration));--tw-ease: var(--ease-in);transition-timing-function:var(--ease-in)}@media (hover: hover){.aui-thread-followup-suggestion:hover{background-color:color-mix(in srgb,hsl(var(--muted)) 80%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-thread-followup-suggestion:hover{background-color:color-mix(in oklab,var(--color-muted) 80%,transparent)}}}.aui-thread-welcome-root{margin-inline:auto;display:flex;width:100%;max-width:var(--thread-max-width);flex-grow:1;flex-direction:column;padding-inline:var(--thread-padding-x)}.aui-thread-welcome-center{display:flex;width:100%;flex-grow:1;flex-direction:column;align-items:center;justify-content:center}.aui-thread-welcome-message{display:flex;width:100%;height:100%;flex-direction:column;justify-content:center;padding-inline:calc(var(--spacing) * 8)}@media (width >= 48rem){.aui-thread-welcome-message{margin-top:calc(var(--spacing) * 20)}}.aui-thread-welcome-message-motion-1{font-size:var(--text-2xl);line-height:var(--tw-leading, var(--text-2xl--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.aui-thread-welcome-message-motion-2{font-size:var(--text-2xl);line-height:var(--tw-leading, var(--text-2xl--line-height));color:color-mix(in srgb,hsl(var(--muted-foreground)) 65%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-thread-welcome-message-motion-2{color:color-mix(in oklab,var(--color-muted-foreground) 65%,transparent)}}.aui-thread-welcome-suggestions{display:grid;width:100%;gap:calc(var(--spacing) * 2)}@media (width >= 40rem){.aui-thread-welcome-suggestions{grid-template-columns:repeat(2,minmax(0,1fr))}}.aui-thread-welcome-suggestion{height:auto;width:100%;flex:1;flex-wrap:wrap;align-items:flex-start;justify-content:flex-start;gap:calc(var(--spacing) * 1);border-radius:var(--radius-xl);border-style:var(--tw-border-style);border-width:1px;padding-inline:calc(var(--spacing) * 4);padding-block:calc(var(--spacing) * 3.5);text-align:left;font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}@media (width >= 40rem){.aui-thread-welcome-suggestion{flex-direction:column}}@media (hover: hover){.aui-thread-welcome-suggestion:where(.dark,.dark *):hover{background-color:color-mix(in srgb,hsl(var(--accent)) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-thread-welcome-suggestion:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-accent) 60%,transparent)}}}.aui-thread-welcome-suggestion-display:nth-child(n+3){display:none}@media (width >= 40rem){.aui-thread-welcome-suggestion-display:nth-child(n+3){display:block}}.aui-thread-welcome-suggestion-text-1{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.aui-thread-welcome-suggestion-text-2{color:var(--color-muted-foreground)}.aui-composer-wrapper{position:relative;margin-inline:auto;display:flex;width:100%;max-width:var(--thread-max-width);flex-direction:column;gap:calc(var(--spacing) * 4);background-color:var(--color-background);padding-inline:var(--thread-padding-x);padding-bottom:calc(var(--spacing) * 4)}@media (width >= 48rem){.aui-composer-wrapper{padding-bottom:calc(var(--spacing) * 6)}}.aui-composer-root{position:relative;display:flex;width:100%;flex-direction:column;border-radius:var(--radius-2xl)}.aui-composer-root:focus-within{--tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.aui-composer-root:focus-within{--tw-ring-color: var(--color-black)}.aui-composer-root:focus-within{--tw-ring-offset-width: 2px;--tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)}.aui-composer-root:where(.dark,.dark *):focus-within{--tw-ring-color: var(--color-white)}.aui-composer-input{max-height:50dvh;min-height:calc(var(--spacing) * 16);width:100%;resize:none;border-top-left-radius:var(--radius-2xl);border-top-right-radius:var(--radius-2xl);border-inline-style:var(--tw-border-style);border-inline-width:1px;border-top-style:var(--tw-border-style);border-top-width:1px;border-color:var(--color-border);background-color:var(--color-muted);padding-inline:calc(var(--spacing) * 4);padding-top:calc(var(--spacing) * 2);padding-bottom:calc(var(--spacing) * 3);font-size:var(--text-base);line-height:var(--tw-leading, var(--text-base--line-height));--tw-outline-style: none;outline-style:none}.aui-composer-input::placeholder{color:var(--color-muted-foreground)}.aui-composer-input:focus{outline-color:var(--color-primary)}.aui-composer-input:where(.dark,.dark *){border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 15%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-input:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-muted-foreground) 15%,transparent)}}.aui-composer-action-wrapper{position:relative;display:flex;align-items:center;justify-content:space-between;border-bottom-right-radius:var(--radius-2xl);border-bottom-left-radius:var(--radius-2xl);border-inline-style:var(--tw-border-style);border-inline-width:1px;border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-color:var(--color-border);background-color:var(--color-muted);padding:calc(var(--spacing) * 2)}.aui-composer-action-wrapper:where(.dark,.dark *){border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 15%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-action-wrapper:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-muted-foreground) 15%,transparent)}}.aui-composer-send{width:calc(var(--spacing) * 8);height:calc(var(--spacing) * 8);border-radius:calc(infinity * 1px);border-style:var(--tw-border-style);border-width:1px;border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-send{border-color:color-mix(in oklab,var(--color-muted-foreground) 60%,transparent)}}@media (hover: hover){.aui-composer-send:hover{background-color:color-mix(in srgb,hsl(var(--primary)) 75%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-send:hover{background-color:color-mix(in oklab,var(--color-primary) 75%,transparent)}}}.aui-composer-send:where(.dark,.dark *){border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 90%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-send:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-muted-foreground) 90%,transparent)}}.aui-composer-send-icon{width:calc(var(--spacing) * 5);height:calc(var(--spacing) * 5)}.aui-composer-cancel{width:calc(var(--spacing) * 8);height:calc(var(--spacing) * 8);border-radius:calc(infinity * 1px);border-style:var(--tw-border-style);border-width:1px;border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-cancel{border-color:color-mix(in oklab,var(--color-muted-foreground) 60%,transparent)}}@media (hover: hover){.aui-composer-cancel:hover{background-color:color-mix(in srgb,hsl(var(--primary)) 75%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-cancel:hover{background-color:color-mix(in oklab,var(--color-primary) 75%,transparent)}}}.aui-composer-cancel:where(.dark,.dark *){border-color:color-mix(in srgb,hsl(var(--muted-foreground)) 90%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-cancel:where(.dark,.dark *){border-color:color-mix(in oklab,var(--color-muted-foreground) 90%,transparent)}}.aui-composer-cancel-icon{width:calc(var(--spacing) * 3.5);height:calc(var(--spacing) * 3.5);fill:var(--color-white)}.aui-composer-cancel-icon:where(.dark,.dark *){width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.aui-composer-cancel-icon:where(.dark,.dark *){fill:var(--color-black)}.aui-composer-attachment-button{--tw-scale-x: 115%;--tw-scale-y: 115%;--tw-scale-z: 115%;scale:var(--tw-scale-x) var(--tw-scale-y);padding:calc(var(--spacing) * 3.5)}@media (hover: hover){.aui-composer-attachment-button:hover{background-color:color-mix(in srgb,hsl(var(--foreground)) 15%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-attachment-button:hover{background-color:color-mix(in oklab,var(--color-foreground) 15%,transparent)}}}@media (hover: hover){.aui-composer-attachment-button:where(.dark,.dark *):hover{background-color:color-mix(in srgb,hsl(var(--background)) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-composer-attachment-button:where(.dark,.dark *):hover{background-color:color-mix(in oklab,var(--color-background) 50%,transparent)}}}.aui-composer-attach{margin-block:calc(var(--spacing) * 2.5);width:calc(var(--spacing) * 8);height:calc(var(--spacing) * 8);padding:calc(var(--spacing) * 2);transition-property:opacity;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration));--tw-ease: var(--ease-in);transition-timing-function:var(--ease-in)}.aui-composer-attachments{display:flex;width:100%;flex-direction:row;gap:calc(var(--spacing) * 3);overflow-x:auto}.aui-attachment-root{position:relative;margin-top:calc(var(--spacing) * 3)}.aui-attachment-content{display:flex;height:calc(var(--spacing) * 12);width:calc(var(--spacing) * 40);align-items:center;justify-content:center;gap:calc(var(--spacing) * 2);border-radius:var(--radius-lg);border-style:var(--tw-border-style);border-width:1px;padding:calc(var(--spacing) * 1)}.aui-attachment-preview-trigger{cursor:pointer;transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}@media (hover: hover){.aui-attachment-preview-trigger:hover{background-color:color-mix(in srgb,hsl(var(--accent)) 50%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-attachment-preview-trigger:hover{background-color:color-mix(in oklab,var(--color-accent) 50%,transparent)}}}.aui-attachment-thumb{display:flex;width:calc(var(--spacing) * 10);height:calc(var(--spacing) * 10);align-items:center;justify-content:center;border-radius:.25rem;border-style:var(--tw-border-style);border-width:1px;background-color:var(--color-muted);font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}.aui-attachment-text{flex-grow:1;flex-basis:calc(var(--spacing) * 0)}.aui-attachment-name{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height));--tw-font-weight: var(--font-weight-bold);font-weight:var(--font-weight-bold);word-break:break-all;text-overflow:ellipsis;color:var(--color-muted-foreground)}.aui-attachment-type{font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height));color:var(--color-muted-foreground)}.aui-attachment-remove{position:absolute;top:calc(var(--spacing) * -3);right:calc(var(--spacing) * -3);width:calc(var(--spacing) * 6);height:calc(var(--spacing) * 6);color:var(--color-muted-foreground)}.aui-attachment-remove>svg{width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4)}.aui-attachment-remove>svg{border-radius:calc(infinity * 1px)}.aui-attachment-remove>svg{background-color:var(--color-background)}.aui-user-message-root{margin-inline:auto;display:grid;width:100%;max-width:var(--thread-max-width);grid-auto-rows:auto;grid-template-columns:minmax(72px,1fr) auto;row-gap:calc(var(--spacing) * 1);padding-inline:var(--thread-padding-x);padding-block:calc(var(--spacing) * 4)}.aui-user-message-root:where(>*){grid-column-start:2}.aui-user-branch-picker{grid-column:1 / -1;grid-column-start:1;grid-row-start:3;margin-right:calc(var(--spacing) * -1);justify-content:flex-end}.aui-user-message-content{grid-column-start:2;border-radius:var(--radius-3xl);background-color:var(--color-muted);padding-inline:calc(var(--spacing) * 5);padding-block:calc(var(--spacing) * 2.5);overflow-wrap:break-word;color:var(--color-foreground)}.aui-user-message-attachments{display:flex;width:100%;flex-direction:row;gap:calc(var(--spacing) * 3);grid-column:1 / -1;grid-column-start:1;grid-row-start:1;justify-content:flex-end}.aui-user-action-bar-root{grid-column-start:1;margin-top:calc(var(--spacing) * 2.5);margin-right:calc(var(--spacing) * 3);display:flex;flex-direction:column;align-items:flex-end}.aui-edit-composer-wrapper{margin-inline:auto;display:flex;width:100%;max-width:var(--thread-max-width);flex-direction:column;gap:calc(var(--spacing) * 4);padding-inline:var(--thread-padding-x)}.aui-edit-composer-root{margin-left:auto;display:flex;width:100%;max-width:87.5%;flex-direction:column;border-radius:var(--radius-xl);background-color:var(--color-muted)}.aui-edit-composer-input{display:flex;min-height:60px;width:100%;resize:none;background-color:transparent;padding:calc(var(--spacing) * 4);color:var(--color-foreground);--tw-outline-style: none;outline-style:none}.aui-edit-composer-footer{margin-inline:calc(var(--spacing) * 3);margin-bottom:calc(var(--spacing) * 3);display:flex;align-items:center;justify-content:center;gap:calc(var(--spacing) * 2);align-self:flex-end}.aui-assistant-message-root{position:relative;margin-inline:auto;display:grid;width:100%;max-width:var(--thread-max-width);grid-template-columns:auto auto 1fr;grid-template-rows:auto 1fr;padding-inline:var(--thread-padding-x);padding-block:calc(var(--spacing) * 4)}.aui-assistant-message-avatar{grid-column-start:1;grid-row-start:1;display:flex;width:calc(var(--spacing) * 8);height:calc(var(--spacing) * 8);flex-shrink:0;align-items:center;justify-content:center;border-radius:calc(infinity * 1px);background-color:var(--color-background);--tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color: var(--color-border)}:where(.aui-assistant-message-root)>.aui-avatar-root{grid-column-start:1;grid-row:1 / -1;grid-row-start:1;margin-right:calc(var(--spacing) * 4)}.aui-assistant-avatar{grid-column-start:1;grid-row:1 / -1;grid-row-start:1;margin-right:calc(var(--spacing) * 4)}.aui-assistant-branch-picker{grid-column-start:2;grid-row-start:2;margin-right:calc(var(--spacing) * 2);margin-left:calc(var(--spacing) * -2)}.aui-assistant-message-content{grid-column:span 2 / span 2;grid-column-start:2;grid-row-start:1;margin-left:calc(var(--spacing) * 4);--tw-leading: calc(var(--spacing) * 7);line-height:calc(var(--spacing) * 7);overflow-wrap:break-word;color:var(--color-foreground)}.aui-message-error-root{margin-top:calc(var(--spacing) * 2);border-radius:var(--radius-md);border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-destructive);background-color:color-mix(in srgb,hsl(var(--destructive)) 10%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-message-error-root{background-color:color-mix(in oklab,var(--color-destructive) 10%,transparent)}}.aui-message-error-root{padding:calc(var(--spacing) * 3);font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height));color:var(--color-destructive)}.aui-message-error-root:where(.dark,.dark *){background-color:color-mix(in srgb,hsl(var(--destructive)) 5%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-message-error-root:where(.dark,.dark *){background-color:color-mix(in oklab,var(--color-destructive) 5%,transparent)}}.aui-message-error-root:where(.dark,.dark *){color:var(--color-red-200)}.aui-message-error-message{overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2}.aui-assistant-action-bar-root{grid-column-start:3;grid-row-start:2;margin-top:calc(var(--spacing) * 3);margin-left:calc(var(--spacing) * 3);display:flex;gap:calc(var(--spacing) * 1);color:var(--color-muted-foreground)}.aui-assistant-action-bar-root[data-floating]{position:absolute}.aui-assistant-action-bar-root[data-floating]{margin-top:calc(var(--spacing) * 2)}.aui-assistant-action-bar-root[data-floating]{border-radius:var(--radius-md)}.aui-assistant-action-bar-root[data-floating]{border-style:var(--tw-border-style);border-width:1px}.aui-assistant-action-bar-root[data-floating]{background-color:var(--color-background)}.aui-assistant-action-bar-root[data-floating]{padding:calc(var(--spacing) * 1)}.aui-assistant-action-bar-root[data-floating]{--tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / .1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / .1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.aui-assistant-action-bar-feedback-positive[data-submitted]{color:var(--color-green-600)}@media (hover: hover){.aui-assistant-action-bar-feedback-positive[data-submitted]:hover{color:var(--color-green-700)}}.aui-assistant-action-bar-feedback-positive:where(.dark,.dark *)[data-submitted]{color:var(--color-green-500)}@media (hover: hover){.aui-assistant-action-bar-feedback-positive:where(.dark,.dark *)[data-submitted]:hover{color:var(--color-green-400)}}.aui-assistant-action-bar-feedback-negative[data-submitted]{color:var(--color-red-600)}@media (hover: hover){.aui-assistant-action-bar-feedback-negative[data-submitted]:hover{color:var(--color-red-700)}}.aui-assistant-action-bar-feedback-negative:where(.dark,.dark *)[data-submitted]{color:var(--color-red-500)}@media (hover: hover){.aui-assistant-action-bar-feedback-negative:where(.dark,.dark *)[data-submitted]:hover{color:var(--color-red-400)}}.aui-branch-picker-root{display:inline-flex;align-items:center;font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height));color:var(--color-muted-foreground)}.aui-branch-picker-state{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium)}.aui-text{white-space:pre-line}.aui-text-running:after{animation:var(--animate-pulse);font-family:var(--font-sans);--tw-content: "●";content:var(--tw-content)}.aui-text-running:after:where(:dir(ltr),[dir=ltr],[dir=ltr] *){margin-left:calc(var(--spacing) * 1)}.aui-text-running:after:where(:dir(rtl),[dir=rtl],[dir=rtl] *){margin-right:calc(var(--spacing) * 1)}.aui-thread-list-root{display:flex;flex-direction:column;align-items:stretch;gap:calc(var(--spacing) * 1.5)}.aui-thread-list-item{display:flex;align-items:center;gap:calc(var(--spacing) * 2);border-radius:var(--radius-lg);transition-property:all;transition-timing-function:var(--tw-ease, var(--default-transition-timing-function));transition-duration:var(--tw-duration, var(--default-transition-duration))}@media (hover: hover){.aui-thread-list-item:hover{background-color:var(--color-muted)}}.aui-thread-list-item:focus-visible{background-color:var(--color-muted)}.aui-thread-list-item:focus-visible{--tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.aui-thread-list-item:focus-visible{--tw-ring-color: var(--color-ring)}.aui-thread-list-item:focus-visible{--tw-outline-style: none;outline-style:none}.aui-thread-list-item[data-active]{background-color:var(--color-muted)}.aui-thread-list-new{display:flex;align-items:center;justify-content:flex-start;gap:calc(var(--spacing) * 1);border-radius:var(--radius-lg);padding-inline:calc(var(--spacing) * 2.5);padding-block:calc(var(--spacing) * 2);text-align:start}@media (hover: hover){.aui-thread-list-new:hover{background-color:var(--color-muted)}}.aui-thread-list-new[data-active]{background-color:var(--color-muted)}.aui-thread-list-item-trigger{flex-grow:1;padding-inline:calc(var(--spacing) * 3);padding-block:calc(var(--spacing) * 2);text-align:start}.aui-thread-list-item-title{font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height))}.aui-thread-list-item-archive{margin-right:calc(var(--spacing) * 1);margin-left:auto;width:calc(var(--spacing) * 4);height:calc(var(--spacing) * 4);padding:calc(var(--spacing) * 4);color:var(--color-foreground)}@media (hover: hover){.aui-thread-list-item-archive:hover{color:color-mix(in srgb,hsl(var(--foreground)) 60%,transparent)}@supports (color: color-mix(in lab,red,red)){.aui-thread-list-item-archive:hover{color:color-mix(in oklab,var(--color-foreground) 60%,transparent)}}}:root{--background: 0 0% 100%;--foreground: 240 10% 3.9%;--card: 0 0% 100%;--card-foreground: 240 10% 3.9%;--popover: 0 0% 100%;--popover-foreground: 240 10% 3.9%;--primary: 240 5.9% 10%;--primary-foreground: 0 0% 98%;--secondary: 240 4.8% 95.9%;--secondary-foreground: 240 5.9% 10%;--muted: 240 4.8% 95.9%;--muted-foreground: 240 3.8% 46.1%;--accent: 240 4.8% 95.9%;--accent-foreground: 240 5.9% 10%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 0 0% 98%;--border: 240 5.9% 90%;--input: 240 5.9% 90%;--ring: 240 10% 3.9%;--chart-1: 12 76% 61%;--chart-2: 173 58% 39%;--chart-3: 197 37% 24%;--chart-4: 43 74% 66%;--chart-5: 27 87% 67%;--radius: .5rem}.dark{--background: 240 10% 3.9%;--foreground: 0 0% 98%;--card: 240 10% 3.9%;--card-foreground: 0 0% 98%;--popover: 240 10% 3.9%;--popover-foreground: 0 0% 98%;--primary: 0 0% 98%;--primary-foreground: 240 5.9% 10%;--secondary: 240 3.7% 15.9%;--secondary-foreground: 0 0% 98%;--muted: 240 3.7% 15.9%;--muted-foreground: 240 5% 64.9%;--accent: 240 3.7% 15.9%;--accent-foreground: 0 0% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 0 0% 98%;--border: 240 3.7% 15.9%;--input: 240 3.7% 15.9%;--ring: 240 4.9% 83.9%;--chart-1: 220 70% 50%;--chart-2: 160 60% 45%;--chart-3: 30 80% 55%;--chart-4: 280 65% 60%;--chart-5: 340 75% 55%}@property --tw-animation-delay{syntax: "*"; inherits: false; initial-value: 0s;}@property --tw-animation-direction{syntax: "*"; inherits: false; initial-value: normal;}@property --tw-animation-duration{syntax: "*"; inherits: false;}@property --tw-animation-fill-mode{syntax: "*"; inherits: false; initial-value: none;}@property --tw-animation-iteration-count{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-enter-opacity{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-enter-rotate{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-enter-scale{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-enter-translate-x{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-enter-translate-y{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-exit-opacity{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-exit-rotate{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-exit-scale{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-exit-translate-x{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-exit-translate-y{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-shadow-color{syntax: "*"; inherits: false;}@property --tw-shadow-alpha{syntax: "<percentage>"; inherits: false; initial-value: 100%;}@property --tw-inset-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-inset-shadow-color{syntax: "*"; inherits: false;}@property --tw-inset-shadow-alpha{syntax: "<percentage>"; inherits: false; initial-value: 100%;}@property --tw-ring-color{syntax: "*"; inherits: false;}@property --tw-ring-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-inset-ring-color{syntax: "*"; inherits: false;}@property --tw-inset-ring-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-ring-inset{syntax: "*"; inherits: false;}@property --tw-ring-offset-width{syntax: "<length>"; inherits: false; initial-value: 0px;}@property --tw-ring-offset-color{syntax: "*"; inherits: false; initial-value: #fff;}@property --tw-ring-offset-shadow{syntax: "*"; inherits: false; initial-value: 0 0 #0000;}@property --tw-translate-x{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-translate-y{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-translate-z{syntax: "*"; inherits: false; initial-value: 0;}@property --tw-duration{syntax: "*"; inherits: false;}@property --tw-ease{syntax: "*"; inherits: false;}@property --tw-scale-x{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-scale-y{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-scale-z{syntax: "*"; inherits: false; initial-value: 1;}@property --tw-content{syntax: "*"; inherits: false; initial-value: "";}@keyframes pulse{50%{opacity:.5}}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0))}}@layer properties{@supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-font-weight: initial;--tw-shadow: 0 0 #0000;--tw-shadow-color: initial;--tw-shadow-alpha: 100%;--tw-inset-shadow: 0 0 #0000;--tw-inset-shadow-color: initial;--tw-inset-shadow-alpha: 100%;--tw-ring-color: initial;--tw-ring-shadow: 0 0 #0000;--tw-inset-ring-color: initial;--tw-inset-ring-shadow: 0 0 #0000;--tw-ring-inset: initial;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-offset-shadow: 0 0 #0000;--tw-border-style: solid;--tw-translate-x: 0;--tw-translate-y: 0;--tw-translate-z: 0;--tw-duration: initial;--tw-ease: initial;--tw-scale-x: 1;--tw-scale-y: 1;--tw-scale-z: 1;--tw-leading: initial;--tw-content: "";--tw-animation-delay: 0s;--tw-animation-direction: normal;--tw-animation-duration: initial;--tw-animation-fill-mode: none;--tw-animation-iteration-count: 1;--tw-enter-opacity: 1;--tw-enter-rotate: 0;--tw-enter-scale: 1;--tw-enter-translate-x: 0;--tw-enter-translate-y: 0;--tw-exit-opacity: 1;--tw-exit-rotate: 0;--tw-exit-scale: 1;--tw-exit-translate-x: 0;--tw-exit-translate-y: 0}}}@layer theme{:root :where(.aui-root),:host :where(.aui-root){--color-zinc-900: oklch(21% .006 285.885);--color-black: #000;--color-white: #fff;--spacing: .25rem;--text-xs: .75rem;--text-xs--line-height: calc(1 / .75);--text-sm: .875rem;--text-sm--line-height: calc(1.25 / .875);--text-lg: 1.125rem;--text-lg--line-height: calc(1.75 / 1.125);--text-xl: 1.25rem;--text-xl--line-height: calc(1.75 / 1.25);--text-2xl: 1.5rem;--text-2xl--line-height: calc(2 / 1.5);--text-3xl: 1.875rem;--text-3xl--line-height: 1.2 ;--text-4xl: 2.25rem;--text-4xl--line-height: calc(2.5 / 2.25);--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--font-weight-extrabold: 800;--tracking-tight: -.025em;--radius-lg: var(--radius);--color-primary: hsl(var(--primary));--color-muted: hsl(var(--muted))}}.aui-shiki-base pre{overflow-x:auto}.aui-shiki-base pre{border-bottom-right-radius:var(--radius-lg);border-bottom-left-radius:var(--radius-lg)}.aui-shiki-base pre{background-color:var(--color-black)}.aui-shiki-base pre{padding:calc(var(--spacing) * 4)}.aui-shiki-base pre{color:var(--color-white)}.aui-code-header-root{margin-top:calc(var(--spacing) * 4);display:flex;align-items:center;justify-content:space-between;gap:calc(var(--spacing) * 4);border-top-left-radius:var(--radius-lg);border-top-right-radius:var(--radius-lg);background-color:var(--color-zinc-900);padding-inline:calc(var(--spacing) * 4);padding-block:calc(var(--spacing) * 2);font-size:var(--text-sm);line-height:var(--tw-leading, var(--text-sm--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold);color:var(--color-white)}.aui-code-header-language{text-transform:lowercase}.aui-code-header-language>span{font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height))}.aui-md-h1{margin-bottom:calc(var(--spacing) * 8);scroll-margin:calc(var(--spacing) * 20);font-size:var(--text-4xl);line-height:var(--tw-leading, var(--text-4xl--line-height));--tw-font-weight: var(--font-weight-extrabold);font-weight:var(--font-weight-extrabold);--tw-tracking: var(--tracking-tight);letter-spacing:var(--tracking-tight)}.aui-md-h1:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-h2{margin-top:calc(var(--spacing) * 8);margin-bottom:calc(var(--spacing) * 4);scroll-margin:calc(var(--spacing) * 20);font-size:var(--text-3xl);line-height:var(--tw-leading, var(--text-3xl--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold);--tw-tracking: var(--tracking-tight);letter-spacing:var(--tracking-tight)}.aui-md-h2:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-h2:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-h3{margin-top:calc(var(--spacing) * 6);margin-bottom:calc(var(--spacing) * 4);scroll-margin:calc(var(--spacing) * 20);font-size:var(--text-2xl);line-height:var(--tw-leading, var(--text-2xl--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold);--tw-tracking: var(--tracking-tight);letter-spacing:var(--tracking-tight)}.aui-md-h3:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-h3:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-h4{margin-top:calc(var(--spacing) * 6);margin-bottom:calc(var(--spacing) * 4);scroll-margin:calc(var(--spacing) * 20);font-size:var(--text-xl);line-height:var(--tw-leading, var(--text-xl--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold);--tw-tracking: var(--tracking-tight);letter-spacing:var(--tracking-tight)}.aui-md-h4:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-h4:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-h5{margin-block:calc(var(--spacing) * 4);font-size:var(--text-lg);line-height:var(--tw-leading, var(--text-lg--line-height));--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.aui-md-h5:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-h5:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-h6{margin-block:calc(var(--spacing) * 4);--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.aui-md-h6:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-h6:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-p{margin-top:calc(var(--spacing) * 5);margin-bottom:calc(var(--spacing) * 5);--tw-leading: calc(var(--spacing) * 7);line-height:calc(var(--spacing) * 7)}.aui-md-p:first-child{margin-top:calc(var(--spacing) * 0)}.aui-md-p:last-child{margin-bottom:calc(var(--spacing) * 0)}.aui-md-a{--tw-font-weight: var(--font-weight-medium);font-weight:var(--font-weight-medium);color:var(--color-primary);text-decoration-line:underline;text-underline-offset:4px}.aui-md-blockquote{border-left-style:var(--tw-border-style);border-left-width:2px;padding-left:calc(var(--spacing) * 6);font-style:italic}.aui-md-ul{margin-block:calc(var(--spacing) * 5);margin-left:calc(var(--spacing) * 6);list-style-type:disc}.aui-md-ul>li{margin-top:calc(var(--spacing) * 2)}.aui-md-ol{margin-block:calc(var(--spacing) * 5);margin-left:calc(var(--spacing) * 6);list-style-type:decimal}.aui-md-ol>li{margin-top:calc(var(--spacing) * 2)}.aui-md-hr{margin-block:calc(var(--spacing) * 5);border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.aui-md-table{margin-block:calc(var(--spacing) * 5);width:100%;border-collapse:separate;--tw-border-spacing-x: calc(var(--spacing) * 0);--tw-border-spacing-y: calc(var(--spacing) * 0);border-spacing:var(--tw-border-spacing-x) var(--tw-border-spacing-y);overflow-y:auto}.aui-md-th{background-color:var(--color-muted);padding-inline:calc(var(--spacing) * 4);padding-block:calc(var(--spacing) * 2);text-align:left;--tw-font-weight: var(--font-weight-bold);font-weight:var(--font-weight-bold)}.aui-md-th:first-child{border-top-left-radius:var(--radius-lg)}.aui-md-th:last-child{border-top-right-radius:var(--radius-lg)}.aui-md-th[align=center]{text-align:center}.aui-md-th[align=right]{text-align:right}.aui-md-td{border-bottom-style:var(--tw-border-style);border-bottom-width:1px;border-left-style:var(--tw-border-style);border-left-width:1px;padding-inline:calc(var(--spacing) * 4);padding-block:calc(var(--spacing) * 2);text-align:left}.aui-md-td:last-child{border-right-style:var(--tw-border-style);border-right-width:1px}.aui-md-td[align=center]{text-align:center}.aui-md-td[align=right]{text-align:right}.aui-md-tr{margin:calc(var(--spacing) * 0);border-bottom-style:var(--tw-border-style);border-bottom-width:1px;padding:calc(var(--spacing) * 0)}.aui-md-tr:first-child{border-top-style:var(--tw-border-style);border-top-width:1px}.aui-md-tr:last-child>td:first-child{border-bottom-left-radius:var(--radius-lg)}.aui-md-tr:last-child>td:last-child{border-bottom-right-radius:var(--radius-lg)}.aui-md-sup>a{font-size:var(--text-xs);line-height:var(--tw-leading, var(--text-xs--line-height))}.aui-md-sup>a{text-decoration-line:none}.aui-md-pre{overflow-x:auto;border-top-left-radius:0!important;border-top-right-radius:0!important;border-bottom-right-radius:var(--radius-lg);border-bottom-left-radius:var(--radius-lg);background-color:var(--color-black);padding:calc(var(--spacing) * 4);color:var(--color-white)}.aui-md-inline-code{border-radius:.25rem;border-style:var(--tw-border-style);border-width:1px;background-color:var(--color-muted);--tw-font-weight: var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.aui-mermaid-diagram{border-bottom-right-radius:var(--radius-lg);border-bottom-left-radius:var(--radius-lg);background-color:var(--color-muted);padding:calc(var(--spacing) * 2);text-align:center}.aui-mermaid-diagram svg{margin-inline:auto}@property --tw-font-weight{syntax: "*"; inherits: false;}@property --tw-tracking{syntax: "*"; inherits: false;}@property --tw-leading{syntax: "*"; inherits: false;}@property --tw-border-style{syntax: "*"; inherits: false; initial-value: solid;}@property --tw-border-spacing-x{syntax: "<length>"; inherits: false; initial-value: 0;}@property --tw-border-spacing-y{syntax: "<length>"; inherits: false; initial-value: 0;}@layer properties{@supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-font-weight: initial;--tw-tracking: initial;--tw-leading: initial;--tw-border-style: solid;--tw-border-spacing-x: 0;--tw-border-spacing-y: 0}}}/*! tailwindcss v4.1.12 | MIT License | https://tailwindcss.com */.instrument-app{height:100vh;width:100vw;position:relative;background:linear-gradient(135deg,#1e1e2e,#2d2d44);color:#fff;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif;overflow:hidden}.status-bar{position:absolute;top:20px;left:20px;z-index:1000;display:flex;align-items:center;gap:10px;background:#000000b3;padding:8px 16px;border-radius:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.status.connected{color:#4ade80}.status.disconnected{color:#f87171}.device-info{color:#94a3b8;font-size:.9em}.instrument-layout{display:grid;grid-template-columns:250px 1fr;height:100vh;gap:1rem;padding:1rem}.aui-thread-list-root{background:#ffffff1a;border-radius:15px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.2);padding:1rem;overflow-y:auto}.aui-thread-list-new{width:100%;margin-bottom:1rem;background:linear-gradient(135deg,#3b82f6,#1d4ed8);color:#fff;border:none;border-radius:10px;padding:12px;font-weight:600;transition:all .3s ease}.aui-thread-list-new:hover{transform:translateY(-1px);box-shadow:0 4px 15px #3b82f64d}.aui-thread-list-item{display:flex;align-items:center;padding:8px;border-radius:8px;margin-bottom:4px;transition:background-color .2s ease}.aui-thread-list-item:hover{background:#ffffff1a}.aui-thread-list-item-trigger{flex:1;text-align:left;background:none;border:none;color:inherit;padding:4px 8px;border-radius:4px}.aui-thread-list-item-title{margin:0;font-size:.9em;color:#e2e8f0}.aui-thread-root{background:#ffffff1a;border-radius:15px;-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.2);display:flex;flex-direction:column;overflow:hidden;box-shadow:0 20px 40px #0000004d}.aui-thread-viewport{flex:1;display:flex;flex-direction:column;overflow:hidden}.aui-thread-viewport-footer{padding:1rem;border-top:1px solid rgba(255,255,255,.1);background:#0003;display:flex;flex-direction:column;gap:1rem}.aui-thread-welcome-root{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:2rem;text-align:center}.aui-thread-welcome-center{margin-bottom:2rem}.aui-thread-welcome-message{font-size:1.5em;font-weight:600;margin-bottom:.5rem;background:linear-gradient(135deg,#3b82f6,#8b5cf6);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.aui-thread-welcome-submessage{color:#94a3b8;font-size:1em;margin-bottom:.5rem}.aui-thread-welcome-instructions{color:#94a3b8;font-size:.9em}.aui-thread-welcome-suggestions{display:flex;flex-wrap:wrap;gap:.5rem;justify-content:center}.aui-thread-welcome-suggestion{background:#ffffff1a;border:1px solid rgba(255,255,255,.2);border-radius:20px;padding:8px 16px;font-size:.9em;color:#e2e8f0;cursor:pointer;transition:all .3s ease}.aui-thread-welcome-suggestion:hover{background:#fff3;transform:translateY(-1px)}.device-controls{display:flex;align-items:center;gap:1rem;padding:.5rem;background:#0000004d;border-radius:10px}.capture-control-btn{background:linear-gradient(135deg,#10b981,#059669);color:#fff;border:none;border-radius:8px;padding:8px;transition:all .3s ease}.capture-control-btn:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 15px #10b9814d}.send-control-btn{background:linear-gradient(135deg,#f59e0b,#d97706);color:#fff;border:none;border-radius:8px;padding:8px;transition:all .3s ease}.send-control-btn:hover:not(:disabled){transform:translateY(-1px);box-shadow:0 4px 15px #f59e0b4d}.device-status{margin-left:auto;font-size:.9em}.status-connected{color:#4ade80}.status-disconnected{color:#f87171}.aui-composer-root{display:flex;align-items:flex-end;gap:.5rem;background:#ffffff1a;border-radius:10px;padding:.5rem}.aui-composer-input{flex:1;background:transparent;border:none;color:#fff;font-size:14px;resize:none;outline:none;min-height:40px;padding:8px}.aui-composer-input::placeholder{color:#94a3b8}.aui-composer-send,.aui-composer-cancel{background:linear-gradient(135deg,#8b5cf6,#7c3aed);color:#fff;border:none;border-radius:8px;padding:8px;transition:all .3s ease}.aui-composer-send:hover,.aui-composer-cancel:hover{transform:translateY(-1px);box-shadow:0 4px 15px #8b5cf64d}.aui-user-message-root,.aui-assistant-message-root{margin:1rem;padding:1rem;border-radius:15px;position:relative}.aui-user-message-root{background:#3b82f633;border-left:4px solid #3b82f6;margin-left:2rem}.aui-assistant-message-root{background:#10b98133;border-left:4px solid #10b981;margin-right:2rem}.aui-user-message-content,.aui-assistant-message-content{line-height:1.6;word-wrap:break-word}.aui-user-action-bar-root,.aui-assistant-action-bar-root{position:absolute;top:.5rem;right:.5rem;display:flex;gap:.25rem;opacity:0;transition:opacity .2s ease}.aui-user-message-root:hover .aui-user-action-bar-root,.aui-assistant-message-root:hover .aui-assistant-action-bar-root{opacity:1}.aui-branch-picker-root{display:flex;align-items:center;gap:.5rem;margin-top:.5rem;font-size:.8em;color:#94a3b8}.aui-button{display:inline-flex;align-items:center;justify-content:center;gap:.5rem;font-weight:500;transition:all .2s ease;cursor:pointer;border:none;outline:none}.aui-button:disabled{opacity:.5;cursor:not-allowed}.aui-button-primary{background:linear-gradient(135deg,#3b82f6,#1d4ed8);color:#fff;padding:8px 16px;border-radius:8px}.aui-button-outline{background:transparent;color:#e2e8f0;border:1px solid rgba(255,255,255,.2);padding:8px 16px;border-radius:8px}.aui-button-ghost{background:transparent;color:#e2e8f0;padding:8px;border-radius:6px}.aui-button-ghost:hover:not(:disabled){background:#ffffff1a}.aui-button-icon{padding:8px;border-radius:6px}.aui-tooltip-content{background:#000000e6;color:#fff;padding:4px 8px;border-radius:4px;font-size:.8em;z-index:1000}.aui-thread-list-root::-webkit-scrollbar,.aui-thread-viewport::-webkit-scrollbar{width:6px}.aui-thread-list-root::-webkit-scrollbar-track,.aui-thread-viewport::-webkit-scrollbar-track{background:#ffffff1a;border-radius:3px}.aui-thread-list-root::-webkit-scrollbar-thumb,.aui-thread-viewport::-webkit-scrollbar-thumb{background:#ffffff4d;border-radius:3px}.aui-thread-list-root::-webkit-scrollbar-thumb:hover,.aui-thread-viewport::-webkit-scrollbar-thumb:hover{background:#ffffff80}.aui-sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.image-preview{position:absolute;bottom:20px;left:20px;background:#000c;padding:15px;border-radius:15px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);max-width:300px;z-index:100}.image-preview h3{margin:0 0 10px;font-size:1em;color:#94a3b8}.image-grid{display:flex;gap:8px;align-items:center}.preview-image{width:60px;height:60px;object-fit:cover;border-radius:8px;border:2px solid rgba(255,255,255,.2)}.more-images{color:#94a3b8;font-size:.9em;padding:0 8px}.absolute{position:absolute}.bottom-4{bottom:1rem}.left-4{left:1rem}.max-w-xs{max-width:20rem}@media (max-width: 768px){.instrument-layout{grid-template-columns:1fr;grid-template-rows:auto 1fr}.aui-thread-list-root{max-height:200px}.image-preview{position:relative;bottom:auto;left:auto;margin-top:20px}}
